export type PrintStatus =
  | {
      status: 0 | 1;
    }
  | {
      status: -1;
      error: string;
    };

export type JobStatus =
  | "正在打印"
  | "暂停"
  | "错误"
  | "准备中"
  | "打印中"
  | "已完成"
  | string;

export interface PrintJob {
  Name: string; // 'Microsoft Print to PDF, 17'
  Document: string; // 文件路径
  JobId: number; // 作业ID
  JobStatus: JobStatus; // 打印状态
  TotalPages: number; // 总页数
  PagesPrinted: number; // 已打印页数
  Owner: string; // 用户名
  TimeSubmitted: string; // 提交时间（WMI格式字符串）
  DriverName?: string;
  PaperSize?: string;
  PaperLength?: number;
  PaperWidth?: number;
  Status?: string;
  Priority?: number;
  // 其他的字段...
  [key: string]: any;
}
