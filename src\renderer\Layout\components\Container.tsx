import type React from "react";

interface ContainerProps {
  children: React.ReactNode;
}

const Container: React.FC<ContainerProps> = ({ children }) => {
  return (
    <div className="warp flex-1 overflow-hidden">
      <div className="relative w-[94vw] h-[calc(100%-4px)] bg-[rgba(7,29,61,.3)]  border-t-2 border-[#123365]">
        <div className="absolute z-10 -top-[2px] left-0 -translate-x-1/2 w-[10px] h-[10px] bg-primary-20"></div>
        <div className="absolute z-10 -top-[2px] right-0 translate-x-1/2 w-[10px] h-[10px] bg-primary-20"></div>
        <div className="absolute z-10  bottom-0 left-0 -translate-x-1/2 translate-y-1/2 w-[10px] h-[10px] bg-primary-20"></div>
        <div className="absolute z-10 bottom-0 right-0 translate-x-1/2 translate-y-1/2 w-[10px] h-[10px] bg-primary-20"></div>

        {children}
      </div>
    </div>
  );
};

export default Container;
