import React from "react";

interface LoadingSpinnerProps {
  /** 加载文本 */
  text?: React.ReactNode;
  /** 是否显示 */
  show?: boolean;
  /** 自定义样式类 */
  className?: string;
}

/**
 * 加载状态组件
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  text = "加载中...",
  show = true,
  className = "",
}) => {
  if (!show) return null;

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="flex flex-col items-center space-y-2">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <div className="text-sm text-primary-default">{text}</div>
      </div>
    </div>
  );
};

export default LoadingSpinner;
