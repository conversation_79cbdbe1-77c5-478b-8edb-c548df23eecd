{"compilerOptions": {"target": "ESNext", "module": "ESNext", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strictNullChecks": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "moduleResolution": "bundler", "resolveJsonModule": true, "jsx": "react-jsx", "allowImportingTsExtensions": false, "noEmit": true, "paths": {"@/*": ["./src/*"], "@assets/*": ["./src/renderer/assets/*"]}}}