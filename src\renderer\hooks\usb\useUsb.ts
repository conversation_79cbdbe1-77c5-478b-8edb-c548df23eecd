import { useAppDispatch, useAppSelector } from "@/renderer/store/hooks";
import { setFiles } from "@/renderer/store/slice/file";
import {
  clearError,
  clearUploadResult,
  detachUsb,
} from "@/renderer/store/slice/usb";
import {
  clearSelection,
  getSelectedFiles,
  readUsbFiles,
  selectUsbPath,
  setUsbPath,
  toggleFileSelection,
} from "@/renderer/store/slice/usb/thunks";
import { useCallback } from "react";

/**
 * U盘检测、文件读取、文件操作等功能
 */
export const useUsb = () => {
  const dispatch = useAppDispatch();
  const usb = useAppSelector(state => state.usb);

  /**
   * 读取U盘文件
   */
  const handleReadFiles = useCallback(async () => {
    try {
      await dispatch(readUsbFiles()).unwrap();
    } catch (error) {
      console.error("读取U盘文件失败:", error);
    }
  }, [dispatch]);

  /**
   * 选择U盘路径
   */
  const handleSelectPath = useCallback(async () => {
    try {
      const path = await dispatch(selectUsbPath()).unwrap();
      if (path) {
        // 选择路径后自动读取文件
        await handleReadFiles();
      }
      return path;
    } catch (error) {
      console.error("选择U盘路径失败:", error);
      return null;
    }
  }, [dispatch, handleReadFiles]);

  /**
   * 设置U盘路径
   */
  const handleSetPath = useCallback(
    async (path: string) => {
      try {
        await dispatch(setUsbPath(path)).unwrap();
        return true;
      } catch (error) {
        console.error("设置U盘路径失败:", error);
        return false;
      }
    },
    [dispatch]
  );

  /**
   * 切换文件选择状态
   */
  const handleToggleFileSelection = useCallback(
    async (fileId: string, selected: boolean) => {
      try {
        await dispatch(toggleFileSelection({ fileId, selected })).unwrap();
      } catch (error) {
        console.error("切换文件选择状态失败:", error);
      }
    },
    [dispatch]
  );

  /**
   * 获取选中的文件
   */
  const handleGetSelectedFiles = useCallback(async () => {
    try {
      const selectedFiles = await dispatch(getSelectedFiles()).unwrap();
      return selectedFiles;
    } catch (error) {
      console.error("获取选中文件失败:", error);
      return [];
    }
  }, [dispatch]);

  /**
   * 清空选择
   */
  const handleClearSelection = useCallback(async () => {
    try {
      await dispatch(clearSelection()).unwrap();
      dispatch(setFiles([]));
    } catch (error) {
      console.error("清空选择失败:", error);
    }
  }, [dispatch]);

  /**
   * 清除错误
   */
  const handleClearError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  /**
   * 断开U盘连接
   */
  const handleDisconnect = useCallback(() => {
    dispatch(detachUsb());
  }, [dispatch]);

  /**
   * 清除上传结果
   */
  const handleClearUploadResult = useCallback(() => {
    dispatch(clearUploadResult());
  }, [dispatch]);

  return {
    files: usb?.files || [],
    drives: usb?.drives || [],
    loading: usb?.loading,
    error: usb?.error,
    currentPath: usb?.currentPath,
    isAttached: usb?.isAttached,
    selectedCount: usb?.selectedCount || 0,
    uploading: usb?.uploading,
    uploadResult: usb?.uploadResult,

    readFiles: handleReadFiles,
    selectPath: handleSelectPath,
    setPath: handleSetPath,
    toggleFileSelection: handleToggleFileSelection,
    getSelectedFiles: handleGetSelectedFiles,
    clearSelection: handleClearSelection,
    clearError: handleClearError,
    disconnect: handleDisconnect,
    clearUploadResult: handleClearUploadResult,
  };
};
