import { twMerge } from "tailwind-merge";

type Props = {
  index: number;
  count: number;
  className?: string;
};

/**
 * 选中区域背景色
 */
const TrackBg: React.FC<Props> = ({ count, index, className }) => {
  const config = {
    left: `${(index / count) * 100}%`,
    width: `${100 / count}%`,
  };

  const classes = twMerge(
    "h-[5.68vw] absolute bottom-0 transition-all duration-300 ease-in-out",
    className
  );

  return (
    <div
      style={{
        background:
          "linear-gradient(to bottom, rgba(7, 59, 183, 0) 0%, rgba(7, 59, 183, 0.6) 100%)",
        left: config.left,
        width: index === -1 ? "0" : config.width,
      }}
      className={classes}
    />
  );
};

export default TrackBg;
