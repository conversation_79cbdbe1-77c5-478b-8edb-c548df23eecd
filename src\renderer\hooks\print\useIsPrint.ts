import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { setIsPrint } from "../../store/slice/client";

/**
 * 获取打印机打印状态
 * @returns
 */
const useIsPrint = () => {
  const dispatch = useAppDispatch();
  const isPrint = useAppSelector(state => state.client?.isPrint);

  useEffect(() => {
    window.electronAPI.util.onPrintStatus(data => {
      dispatch(setIsPrint(!Math.abs(data.status)));
    });
  }, [dispatch]);

  return {
    isPrint,
  };
};

export default useIsPrint;
