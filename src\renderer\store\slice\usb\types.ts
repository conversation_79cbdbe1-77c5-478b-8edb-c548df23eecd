import type { UsbFileInfo, UsbInfo } from "@/types";

// // 驱动器信息接口
// export interface DriveInfo {
//   device: string;
//   description: string;
//   size: number;
//   mountpoints: Array<{ path: string }>;
//   isUSB: boolean;
//   isRemovable: boolean;
// }

// 文件上传结果接口
export interface UploadResult {
  success: boolean;
  message: string;
  uploadedFiles: string[];
  failedFiles: string[];
}

// U盘状态接口
export interface UsbState {
  // 文件列表
  files: UsbFileInfo[];
  // 驱动器列表
  drives: UsbInfo[];
  // 加载状态
  loading: boolean;
  // 错误信息
  error: string | null;
  // 当前U盘路径
  currentPath: string | null;
  // 是否已插入U盘
  isAttached: boolean;
  // 选中的文件数量
  selectedCount: number;
  // 上传状态
  uploading: boolean;
  // 上传结果
  uploadResult: UploadResult | null;
}
