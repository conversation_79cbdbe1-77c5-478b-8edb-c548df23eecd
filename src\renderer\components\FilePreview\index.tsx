import { UsbFileInfo } from "@/types";
import { AnimatePresence, motion } from "motion/react";
import { useEffect, useState } from "react";
import FilePreviewHeader from "./FilePreviewHeader";
import FileRender from "./FileRender";
import ThumbnailList from "./ThumbnailList";

interface Props {
  /** 是否显示 */
  index?: number;

  /** 文件列表 */
  files: UsbFileInfo[];

  /** 是否打开 */
  open: boolean;

  /** 关闭预览 */
  onClose: () => void;

  /** PDF 自定义样式 */
  pdfClassName?: string;
}

const FilePreview: React.FC<Props> = ({
  files,
  open,
  onClose,
  index = 0,
  pdfClassName,
}) => {
  const [activeIndex, setActiveIndex] = useState(index);

  useEffect(() => {
    setActiveIndex(index);
  }, [index]);

  const activeFile = activeIndex !== -1 ? files[activeIndex] : null;

  return (
    <AnimatePresence>
      {open && (
        <motion.div
          className="fixed top-0 left-0 w-full h-full flex flex-col bg-black/50 z-50"
          layout
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <FilePreviewHeader
            index={activeIndex}
            name={activeFile?.name || ""}
            count={files.length}
            onClose={onClose}
          />

          <div className="flex-1 flex items-center justify-center p-4">
            {activeFile && (
              <FileRender file={activeFile} pdfClassName={pdfClassName} />
            )}

            <ThumbnailList
              files={files}
              activeIndex={activeIndex}
              setActiveIndex={setActiveIndex}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FilePreview;
