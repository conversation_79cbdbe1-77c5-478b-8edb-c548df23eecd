import QRCodeStyling from "qr-code-styling";
import React, { useEffect, useRef, useState } from "react";

interface QRCodeProps {
  // 二维码内容
  value: string;
  // 二维码logo
  logoImage?: string;
  // 下载文件名
  downloadFileName?: string;
  // 是否显示下载按钮
  showDownloadButton?: boolean;
  // 限制最大二维码大小，默认 300
  maxSize?: number;
}

const QRCode: React.FC<QRCodeProps> = ({
  value,
  logoImage,
  downloadFileName = "qrcode",
  showDownloadButton = false,
  maxSize = Infinity,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const qrRef = useRef<QRCodeStyling | null>(null);
  const [size, setSize] = useState(100); // 初始尺寸

  // 初始化二维码
  useEffect(() => {
    qrRef.current = new QRCodeStyling({
      width: size,
      height: size,
      data: value,
      image: logoImage,
      qrOptions: {
        errorCorrectionLevel: "H",
      },
      imageOptions: {
        crossOrigin: "anonymous",
        margin: 0,
      },
      dotsOptions: {
        color: "#000",
        type: "rounded",
      },
      backgroundOptions: {
        color: "#ffffff",
      },
      cornersSquareOptions: {
        type: "square",
      },
    });
  }, [logoImage, size, value]);

  // 容器大小变化时更新尺寸
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const observer = new ResizeObserver(entries => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        const newSize = Math.min(maxSize, Math.floor(width));
        setSize(newSize);
        qrRef.current?.update({ width: newSize, height: newSize });
        container.innerHTML = "";
        qrRef.current?.append(container);
      }
    });

    observer.observe(container);
    return () => observer.disconnect();
  }, [maxSize]);

  // 数据变更时更新二维码
  useEffect(() => {
    qrRef.current?.update({ data: value });
  }, [value]);

  const handleDownload = () => {
    qrRef.current?.download({
      name: downloadFileName,
      extension: "png",
    });
  };
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      {/* 二维码居中区域 */}
      <div
        ref={containerRef}
        className="w-full max-w-full flex justify-center items-center"
      />

      {/* 下载按钮 - 绝对定位到底部 */}
      {showDownloadButton && (
        <button
          onClick={handleDownload}
          className="absolute bottom-0 translate-y-[150%] left-1/2 -translate-x-1/2 rounded bg-blue-600 px-4 py-1 text-white hover:bg-blue-700"
        >
          下载二维码
        </button>
      )}
    </div>
  );
};

export default QRCode;
