import type { UsbFileInfo, UsbInfo } from "@/types";
import type mammoth from "mammoth";
import type { Result as PdfParseResult } from "pdf-parse";

export interface UsbAPI {
  /**
   * 监听U盘渲染端加载完毕
   * @param callback 回调函数
   * @returns 返回一个取消监听的函数
   */
  onUsbRenderReady: () => Promise<{
    attach: boolean;
    usbList: UsbInfo[];
  }>;
  /**
   * 监听U盘设备变化
   * @param callback 回调函数
   * @returns 返回一个取消监听的函数
   */
  onUsbChange: (
    callback: (data: {
      loading: boolean;
      real?: boolean;
      attach?: boolean;
      usbList?: UsbInfo[];
    }) => void
  ) => void;
  /**
   * 读取U盘文件
   * @returns 返回U盘文件列表
   */
  readFiles: () => Promise<UsbFileInfo[]>;
  /**
   * 选择U盘路径
   * @returns 返回U盘路径
   */
  selectPath: () => Promise<string | null>;
  /**
   * 设置U盘路径
   * @param path 路径
   * @returns 返回是否成功
   */
  setPath: (path: string) => Promise<boolean>;
  /**
   * 读取文件
   * @param filePath 文件路径
   * @returns 返回文件内容
   */
  readFile: (filePath: string) => Promise<Buffer>;
  /**
   * 获取文件信息
   * @param filePath 文件路径
   * @returns 返回文件信息
   */
  getFileInfo: (filePath: string) => Promise<UsbFileInfo | null>;
  /**
   * 切换文件选择状态
   * @param fileId 文件ID
   * @param selected 是否选中
   * @returns 返回是否成功
   */
  toggleFileSelection: (fileId: string, selected: boolean) => Promise<boolean>;
  /**
   * 获取选中的文件
   * @returns 返回选中的文件列表
   */
  getSelectedFiles: () => Promise<UsbFileInfo[]>;
  /**
   * 清空选中的文件
   * @returns 返回是否成功
   */
  clearSelection: () => Promise<boolean>;
  /**
   * 读取PDF文件
   * @param filePath 文件路径
   * @returns 返回PDF文件内容
   */
  readPdfFile: (filePath: string | Buffer) => Promise<PdfParseResult>;
  /**
   * 读取DOCX文件
   * @param filePath 文件路径
   * @returns 返回DOCX文件内容
   */
  readDocxFile: (
    filePath: string | Buffer
  ) => Promise<ReturnType<typeof mammoth.extractRawText>>;
}

export default UsbAPI;
