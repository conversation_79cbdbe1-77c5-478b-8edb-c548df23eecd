import circle from "@assets/step-three/circle.png";
import { AnimatePresence, motion } from "motion/react";
import React from "react";

interface Props {
  /** 是否显示 */
  visible?: boolean;
}

const PrintStatus: React.FC<Props> = ({ visible = false }) => {
  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{
            duration: 0.2,
            ease: "easeIn",
          }}
          className="absolute left-0 top-0 w-full h-full flex items-center justify-center"
        >
          <div className="px-[2vw] h-[8.54vw] z-10 flex items-center justify-center gap-[1vw] bg-mask/90 rounded-[.63vw]">
            <div className="font-ZQKLKT-Bold text-[2.4vw] text-white">
              正在打印中，请从设备出口取走文件
            </div>

            <motion.img
              src={circle}
              alt="circle"
              className="w-[5vw] h-[5vw]"
              animate={{
                rotate: 360,
              }}
              transition={{
                rotate: { duration: 5, repeat: Infinity, ease: "linear" },
              }}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PrintStatus;
