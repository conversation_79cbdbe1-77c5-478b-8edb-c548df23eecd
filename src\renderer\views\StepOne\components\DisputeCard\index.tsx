import bg from "@assets/step-one/card-bg.png";

type DisputeCardProps = {
  /** 使用次数 */
  useCount: number;
  /** 打印次数 */
  printCount: number;
  /** 纠纷类型 */
  disputeType: string;
  /** 纠纷描述 */
  disputeDescription: string;
  /** 点击事件 */
  onClick: () => void;
};

const DisputeCard: React.FC<DisputeCardProps> = ({
  useCount = 0,
  printCount = 0,
  disputeType,
  disputeDescription,
  onClick,
}) => {
  return (
    <div
      className="relative w-[20.938vw] h-[10.729vw] cursor-pointer"
      style={{
        backgroundImage: `url(${bg})`,
        backgroundSize: "100% 100%",
      }}
      onClick={onClick}
    >
      {/* 使用次数 */}
      <div className="flex items-end absolute top-[0.72vw] left-[3.18vw] pl-[1vw] text-primary-90 italic font-bold leading-none">
        <div className="text-[0.833vw]">{useCount}</div>
        <div className="text-[0.625vw]">/次</div>
      </div>

      {/* 打印次数 */}
      <div className="flex items-end absolute top-[0.72vw] left-[12.86vw] pl-[1vw] text-primary-90 italic font-bold leading-none">
        <div className="text-[0.833vw]">{printCount}</div>
        <div className="text-[0.625vw]">/次</div>
      </div>

      {/* 纠纷类型 */}
      <div className="absolute font-ZQKLKT-Bold top-[4.3vw] left-[1vw] text-[1.667vw] leading-none text-transparent bg-clip-text bg-gradient-to-b from-[#ffffff] via-[#b6cae6] to-[#6c94cd]">
        {disputeType}
      </div>
      {/* 纠纷描述 */}
      <div className="absolute top-[6.63vw] left-[1vw] text-primary-90 text-[.833vw] whitespace-pre-wrap leading-[1.2]">
        {disputeDescription.slice(0, 35)}
      </div>
    </div>
  );
};

export default DisputeCard;
