import { motion } from "motion/react";
import type React from "react";
import { twMerge } from "tailwind-merge";

interface Props {
  className?: string;
  onClick?: () => void;
  children: React.ReactNode;
}

const BlueButton: React.FC<Props> = ({ onClick, children, className }) => {
  return (
    <motion.div
      className={twMerge(
        "w-max h-max py-[0.73vw] px-[2.45vw] rounded-full text-white text-[1.25vw] leading-none cursor-pointer",
        className
      )}
      style={{
        background: "linear-gradient(-28deg,#0048ff 0%,#00a2ff 100%)",
        boxShadow: "0 0 1vw 0.1vw rgba(0, 72, 255, 0.8)",
      }}
      onClick={onClick}
      whileTap={{
        scale: 0.95,
      }}
    >
      {children}
    </motion.div>
  );
};

export default BlueButton;
