import { addPublicPrefix } from "@/main/utils/path";
import type { UsbFileInfo } from "@/types";
import React from "react";
import UsbFile from "./UsbFile";

const UsbFileList: React.FC<{
  files: UsbFileInfo[];
  onChange: (checked: boolean, file: UsbFileInfo) => void;
}> = ({ onChange, files }) => {
  return (
    <div className="h-full overflow-y-auto px-[1vw] custom-scrollbar scrollbar-mb-1vw">
      <div className="grid grid-cols-3 gap-[1.77vw]">
        {files.map((file, index) => (
          <UsbFile
            key={index}
            type={file.type}
            name={file.name}
            thumbnail={addPublicPrefix(file.path)}
            active={file.selected}
            onChange={checked => onChange(checked, file)}
          />
        ))}
      </div>
    </div>
  );
};

export default UsbFileList;
