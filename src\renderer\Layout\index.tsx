import BG from "@assets/layout/bg.jpg";
import { Outlet } from "react-router-dom";
import PrintStatus from "../components/PrintStatus";
import { useIsPrint } from "../hooks";
import useAnalysis from "../hooks/once/useAnalysis";
import useUsbMount from "../hooks/usb/useUsbMount";
import useStep from "../hooks/useStep";
import Container from "./components/Container";
import Header from "./components/Header";
import StepBar from "./components/StepBar";
import TechSupport from "./components/TechSupport";

export default function Layout() {
  const step = useStep();

  useUsbMount();

  useAnalysis();

  const { isPrint } = useIsPrint();

  return (
    <div
      style={{
        backgroundImage: `url(${BG})`,
        backgroundSize: "100% 100%",
      }}
      className="h-screen aspect-vi deo bg-cover bg-center bg-no-repeat relative flex flex-col overflow-hidden"
    >
      <Header />

      {/* 步骤条 */}
      <StepBar step={step} />

      <Container>
        <Outlet />
      </Container>

      {/* 技术支持 */}
      <TechSupport />

      <PrintStatus visible={isPrint} />
    </div>
  );
}
