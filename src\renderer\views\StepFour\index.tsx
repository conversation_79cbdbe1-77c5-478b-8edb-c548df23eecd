import { addPublicPrefix } from "@/main/utils/path";
import CompleteFillTips from "@/renderer/components/CompleteFillTips";
import FileRender from "@/renderer/components/FilePreview/FileRender";
import QRCodeMask from "@/renderer/components/QRCodeMask";
import SplitLine from "@/renderer/components/SplitLine";
import Domain from "@/renderer/config/domain";
import useIframeMessenger, {
  type DocxMessage,
} from "@/renderer/hooks/useIframeMessage";
import { uploadFile } from "@/renderer/service/dispute";
import { useAppSelector } from "@/renderer/store/hooks";
import { pickPageFiles } from "@/renderer/store/slice/client";
import { combineFileText } from "@/renderer/store/slice/file";
import { getDisputeName } from "@/renderer/utils/mapping/formMapping";
import { STATUS_CODE } from "@/renderer/utils/request";
import { nanoid } from "nanoid";
import { getDocument } from "pdfjs-dist";
import * as R from "ramda";
import React, { useEffect, useState } from "react";
import Tools from "./Tools";

export default function StepFour() {
  const enterType = useAppSelector(state => state.client?.enterType);
  const pickFiles = useAppSelector(pickPageFiles);
  const files = useAppSelector(combineFileText);
  const disputeType = useAppSelector(state => state.client?.disputeType);
  const disputePageType = useAppSelector(
    state => state.client?.disputePageType
  );
  const client = useAppSelector(state => state.client);

  // 二维码
  const [qrCode, setQrCode] = useState<{
    loading: boolean;
    data: string;
  } | null>(null);

  const [duplex, setDuplex] = useState(false);

  const handlePrint = async (data: DocxMessage) => {
    const filePath = await window.electronAPI.util.saveFileToTemp(
      data.payload.data,
      getDisputeName(disputeType, disputePageType) + ".docx"
    );
    if (filePath) {
      // 将word转为pdf
      const pdf = await window.electronAPI.libreOffice.convertToPdf(filePath);

      if (pdf.outputPath) {
        // TODO:通过纸张模拟打印时间、无法或者真实状态
        const { numPages } = await getDocument(
          addPublicPrefix(pdf.outputPath, { remote: "ocx" })
        ).promise;

        await window.electronAPI.util.print(pdf.outputPath, duplex, numPages);
      }
    }
  };

  /**
   * 处理扫描word消息
   * @param data 扫描word消息
   */
  const handleScanWord = async (data: DocxMessage) => {
    // 如果存在未填写的字段，则显示提示
    const isNonComplete = data.payload.emptyFields.length > 0;
    setCompleteVisible(isNonComplete);
    if (isNonComplete) return;

    setQrCode({
      loading: true,
      data: "",
    });

    const tid = nanoid(6);
    if (!client?.mid) return;

    const file = new File(
      [data.payload.data],
      getDisputeName(disputeType, disputePageType) + ".docx",
      {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      }
    );

    const res = await uploadFile({
      file: [file],
      mid: client?.mid,
      tid,
    });

    if (res.code === STATUS_CODE.SUCCESS) {
      setQrCode({
        loading: false,
        data: res.data[0],
      });
    } else {
      setQrCode(null);
    }
  };

  /**
   * 处理扫描pdf消息
   * TODO：使用libre-office 实现
   * @param data 扫描pdf消息
   */
  const handleScanPdf = async (data: DocxMessage) => {
    // 如果存在未填写的字段，则显示提示
    const isNonComplete = data.payload.emptyFields.length > 0;
    setCompleteVisible(isNonComplete);
    if (isNonComplete) return;
    setQrCode({
      loading: true,
      data: "",
    });
    const tid = nanoid(6);
    if (!client?.mid) return;
    const tmpPath = await window.electronAPI.util.saveFileToTemp(
      data.payload.data,
      "scan-pdf.docx"
    );
    if (tmpPath) {
      const filePath =
        await window.electronAPI.libreOffice.convertToPdf(tmpPath);

      if (filePath.outputPath) {
        const buffer = await window.electronAPI.util.readFileAsBuffer(
          filePath.outputPath
        );

        const file = new File(
          [buffer],
          getDisputeName(disputeType, disputePageType) + ".pdf",
          {
            type: "application/pdf",
          }
        );

        const res = await uploadFile({
          file: [file],
          mid: client?.mid,
          tid,
        });

        if (res.code === STATUS_CODE.SUCCESS) {
          setQrCode({
            loading: false,
            data: res.data[0],
          });
        } else {
          setQrCode(null);
        }
      }
    }
  };

  // 监听iframe消息
  const { iframeRef, sendMessage } = useIframeMessenger({
    targetOrigin: "*",
    onMessage: async data => {
      switch (data.type) {
        case "POST_DOC_ARRAY_BUFFER":
          if (data.payload.type === "download") {
            window.electronAPI.util.saveFileToPath(
              data.payload.data,
              getDisputeName(disputeType, disputePageType) + ".docx"
            );
          } else if (data.payload.type === "print") {
            handlePrint(data);
          } else if (data.payload.type === "scanPdf") {
            handleScanPdf(data);
          } else if (data.payload.type === "scanWord") {
            handleScanWord(data);
          }
          break;
        case "POST_VALUE":
          setCompleteVisible(!!data.payload.emptyFields.length);
          break;
        default:
          break;
      }
    },
  });

  // 监听passage
  useEffect(() => {
    if (!client?.passage) return;

    const merged = Object.entries(client.passage)
      .filter(([_k, v]) => v.text !== null)
      .map(([_k, v]) => v.text)
      .reduce<Record<string, any>>(
        (acc, text) => (acc && text ? R.mergeDeepRight(acc, text) : acc),
        {}
      );

    if (Object.keys(merged).length === 0) return;

    sendMessage({
      type: "SET_VALUE",
      payload: merged,
    });
  }, [client?.passage, sendMessage]);

  // 完善提示
  const [completeVisible, setCompleteVisible] = useState(false);
  const printUrl = `${Domain.docx}/doc/?id=${pickFiles?.id}`;

  return (
    <div className="h-[74vh] mt-[1.5vh] ml-[2vh] mr-[2vh]">
      <div className="flex gap-[1.3vw] h-full overflow-hidden">
        {/* 文档显示区域 */}
        <div className="h-full w-[25.31vw] flex flex-col gap-[2px] overflow-auto  border-4 border-primary-40 rounded-sm bg-white">
          {enterType === "manual" ? (
            <>
              {pickFiles?.files.map(file => (
                <FileRender
                  className="w-full h-full flex-shrink-0"
                  key={file.id}
                  file={file}
                  skeletonClassName="h-[45.78vw]"
                />
              ))}
            </>
          ) : (
            <>
              {files?.map((file, index) => (
                <React.Fragment key={index}>
                  {file.file && (
                    <FileRender
                      className="w-full flex-shrink-0"
                      key={file.id}
                      file={file.file}
                      skeletonClassName="h-[45.78vw]"
                    />
                  )}
                </React.Fragment>
              ))}
            </>
          )}
        </div>

        <SplitLine className="self-center" />

        {/* 文档编辑区 */}
        <div className="h-full w-[52vw] grow overflow-auto border-4 rounded-sm border-primary-40 bg-white">
          <iframe ref={iframeRef} src={printUrl} className="w-full h-full" />
        </div>

        {/* 工具区 */}
        <Tools
          duplex={duplex}
          setDuplex={setDuplex}
          sendMessage={sendMessage}
        />

        <CompleteFillTips
          visible={completeVisible}
          onClose={() => setCompleteVisible(false)}
          onComplete={() => {
            setCompleteVisible(false);
          }}
        />

        <QRCodeMask
          loading={qrCode?.loading}
          title="扫码下载"
          desc="您可以通过手机扫码查看并下载"
          value={`${import.meta.env.VITE_PUBLIC_URL}${qrCode?.data}`}
          onClose={() => setQrCode(null)}
          visible={!!qrCode}
        />
      </div>
    </div>
  );
}
