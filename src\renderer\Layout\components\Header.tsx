import useFormatDate from "@/renderer/hooks/useFormatDate";
import badgeCourts from "@assets/common/badge-courts.png";
import dateBg from "@assets/layout/date-bg.png";
import { twMerge } from "tailwind-merge";

const Header = () => {
  const { dateStr, weekdayStr, hour, minute } = useFormatDate();

  return (
    <div className="flex items-center justify-between h-[7.41vh] pl-[1.67vw]">
      <div className="flex items-center">
        <div className="w-[2.45vw]">
          <img
            src={badgeCourts}
            alt="badge-courts"
            className="w-full h-[5vh] object-contain"
          />
        </div>
        <SplitLine className="px-4" />
        <div className="flex flex-col justify-center">
          <h1
            className="text-white font-medium"
            style={{
              fontSize: "min(1.67vW, 2.96vh)",
            }}
          >
            合肥市蜀山区人民法院
          </h1>
          <p
            className="text-white text-justify after:inline-block after:w-full after:h-0 leading-none"
            style={{
              fontSize: "min(0.73vW, 1.3vh)",
            }}
          >
            {"he fei shi shu shan qu ren min fa yuan".toUpperCase()}
          </p>
        </div>
      </div>

      <div
        className="flex items-center h-full relative"
        style={{
          width: "min(36.15vw, 64.26vh)",
        }}
      >
        <img
          src={dateBg}
          alt="date-bg"
          className="absolute top-0 object-contain"
          style={{
            width: "min(29.48vw, 52.41vh)",
          }}
        />

        <div
          className="relative z-10 font-ZQKLKT-Bold text-gradient h-full flex items-center whitespace-nowrap"
          style={{
            filter: "drop-shadow(0 2px 1px rgba(0, 0, 0, 1))",
            fontSize: "min(1.88vw, 3.33vh)",
            paddingLeft: "min(4.11vw, 7.41vh)",
          }}
        >
          要素表智能回填系统
        </div>

        <SplitLine className="px-4" />

        <div className="flex flex-col justify-center items-center leading-none">
          <div
            className="font-bold text-gradient"
            style={{
              fontSize: "min(1.15vw, 2.04vh)",
            }}
          >
            {dateStr}
          </div>
          <div
            className="font-bold text-gradient"
            style={{
              fontSize: "min(1.15vw, 2.04vh)",
              lineHeight: 1.2,
            }}
          >
            {weekdayStr}
          </div>
        </div>
        <div
          className="font-bold text-gradient whitespace-nowrap"
          style={{
            fontSize: "min(2.19vw, 3.89vh)",
            marginLeft: "min(0.94vw, 1.67vh)",
          }}
        >
          {hour}
          <div className="inline-block animate-blink">
            <span className="text-gradient">:</span>
          </div>
          {minute}
        </div>
      </div>
    </div>
  );
};

const SplitLine: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div
      className={twMerge("flex", className)}
      style={{
        height: "min(1.88vw, 3.33vh)",
        paddingInline: "min(1.57vw, 0.89vh)",
      }}
    >
      <div className="bg-[#184784] w-[2px] h-full"></div>
      <div className="bg-[#021735] w-[2px] h-full"></div>
    </div>
  );
};

export default Header;
