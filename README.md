# ShowInfo Formo

一个基于 Electron + React + TypeScript + TailwindCSS 的桌面应用程序项目。

## 开发环境

### Windows 系统

1. **Python** - 版本 3.8+ (用于编译原生模块)

   ```bash
   # 下载地址: https://www.python.org/
   python --version
   ```

2. **Visual Studio Build Tools** - 用于编译 C++ 原生模块

   ```bash
   # 下载地址: https://visualstudio.microsoft.com/visual-cpp-build-tools/
   # 安装时选择 "C++ build tools" 工作负载
   ```

#### macOS 系统

1. **Python** - 版本 3.8+ (系统通常已预装)

   ```bash
   python3 --version
   ```

## 项目结构

```
showinfo-formo/
├── src/
│   ├── main.ts          # Electron 主进程
│   ├── preload.ts       # 预加载脚本
│   ├── renderer.ts      # 渲染进程入口
│   └── renderer/        # React 应用
│       ├── App.tsx      # 主应用组件
│       ├── main.tsx     # React 入口
│       ├── components/  # 通用组件
│       ├── router/      # 路由配置
│       ├── store/       # Redux 状态管理
│       └── views/       # 页面视图
├── vite.*.config.ts     # Vite 配置文件
├── forge.config.ts      # Electron Forge 配置
└── tsconfig.json        # TypeScript 配置
```

## 开发命令

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建应用
pnpm package

# 创建安装包
pnpm make

# 代码质量检查
pnpm lint              # ESLint 检查
pnpm lint:fix          # 自动修复 ESLint 问题
pnpm format            # Prettier 格式化
pnpm format:check      # 检查格式化
pnpm type-check        # TypeScript 类型检查
pnpm check            # 完整的代码质量检查

# 清理命令
pnpm clean            # 清理构建文件
pnpm clean:all        # 清理所有文件包括依赖
pnpm reinstall        # 重新安装依赖
```

## 已解决的技术问题

### 1. TailwindCSS Vite 插件模块解析问题

**问题描述**:

```
找不到模块"@tailwindcss/vite"或其相应的类型声明
```

**根本原因**:
TypeScript 的 `moduleResolution: "node"` 配置无法正确解析新版本 TailwindCSS Vite 插件的 `.mts` 类型声明文件。

**解决方案**:
更新 `tsconfig.json` 配置：

```json
{
  "compilerOptions": {
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "noEmit": true
  }
}
```

## 许可证

MIT License

---

**维护者**: JingpengZhang (<EMAIL>)
**创建时间**: 2025年
**最后更新**: 完善代码规范配置
