import { <PERSON>Button, GhostButton } from "@/renderer/components/Button";
import MaxLimit from "@/renderer/components/MaxLimit";
import { useUsb } from "@/renderer/hooks";
import useFileAnalysis from "@/renderer/hooks/useFileAnalysis";
import { useAppDispatch } from "@/renderer/store/hooks";
import { setFiles } from "@/renderer/store/slice/file";
import type { UsbFileInfo } from "@/types";
import { AnimatePresence, motion } from "motion/react";
import React, { useState } from "react";
import UsbFileList from "./UsbFileList";
import UsbInsertTips from "./UsbInsertTips";

/** 文件数量限制 */
const MAX_FILE_COUNT = 10;

const UsbArea: React.FC = () => {
  const dispatch = useAppDispatch();
  const {
    selectedCount,
    isAttached,
    files: usbFiles,
    toggleFileSelection,
    getSelectedFiles,
    clearSelection,
  } = useUsb();
  const { fileAnalysis } = useFileAnalysis();

  const [maxLimitVisible, setMaxLimitVisible] = useState(false);

  /** 取消选择U盘蔬菜 */
  const handleCancel = () => {
    clearSelection();
  };

  /** 确认选择U盘素材 */
  const handleConfirm = async () => {
    const selectedFiles = await getSelectedFiles();
    dispatch(setFiles(selectedFiles));
  };

  const handleChange = async (checked: boolean, file: UsbFileInfo) => {
    // 如果选中且文件数量超过限制，则显示提示
    if (checked && selectedCount >= MAX_FILE_COUNT) {
      setMaxLimitVisible(true);
      return;
    }

    // 分析文件
    fileAnalysis(file, checked);
    toggleFileSelection(file.id, checked);
  };

  return (
    <div className="h-full overflow-hidden py-[1.13vw] px-[.5vw] relative">
      {/* 未插入U盘 */}
      {!isAttached && <UsbInsertTips />}

      {/* 文件数量限制提示 */}
      <MaxLimit
        visible={isAttached && maxLimitVisible}
        maxCount={MAX_FILE_COUNT}
        onClose={() => {
          setMaxLimitVisible(false);
        }}
      />

      {/* 已插入U盘 */}
      {isAttached && <UsbFileList files={usbFiles} onChange={handleChange} />}

      <AnimatePresence>
        {isAttached && selectedCount > 0 && (
          <motion.div
            initial={{ y: "100%", opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: "100%", opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: "easeOut",
              type: "keyframes",
            }}
            className="absolute bottom-0 left-0 flex w-full h-[9.79vw] bg-gradient-to-t from-deepOcean-90/90 via-deepOcean-90/50 to-transparent z-[2] pointer-events-none"
          >
            <div className="flex gap-[1.09vw] mt-auto ml-auto mr-[1.56vw] mb-[1.35vw] pointer-events-auto">
              <GhostButton className="px-[1.4vw]" onClick={handleCancel}>
                取消选择
              </GhostButton>
              <BlueButton className="px-[1.4vw]" onClick={handleConfirm}>
                ({selectedCount})确认选择
              </BlueButton>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default UsbArea;
