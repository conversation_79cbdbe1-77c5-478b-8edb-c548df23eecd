import SplitLine from "@/renderer/components/SplitLine";
import React from "react";
import FileArea from "./components/FIleArea";
import UploadArea from "./components/UploadArea";

const StepTwo: React.FC = () => {
  return (
    <div className="relative w-full h-full overflow-hidden flex px-[1.04vw] pt-[.83vw]">
      {/* 文件上传/选择区域 */}
      <UploadArea />

      {/* 分割线 */}
      <SplitLine className="w-[.31vw] h-full ml-[.9vw] mr-[1.83vw]" />

      {/* 文件区域 */}
      <FileArea />
    </div>
  );
};

export default StepTwo;
