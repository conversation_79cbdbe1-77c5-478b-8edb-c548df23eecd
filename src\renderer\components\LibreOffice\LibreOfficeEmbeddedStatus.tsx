import { motion } from "motion/react";
import { useEffect, useState } from "react";

export interface LibreOfficeInfo {
  mode: "embedded" | "system";
  version: string;
  path: string;
  available: boolean;
}

/**
 * LibreOffice嵌入状态组件
 */
const LibreOfficeEmbeddedStatus: React.FC = () => {
  const [info, setInfo] = useState<LibreOfficeInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchInfo = async () => {
      try {
        const libreOfficeInfo = await window.electronAPI.libreOffice.getInfo();
        setInfo(libreOfficeInfo);
      } catch (error) {
        console.error("获取LibreOffice信息失败:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchInfo();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center space-x-2 text-gray-600">
        <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
        <span>检查LibreOffice状态...</span>
      </div>
    );
  }

  if (!info) {
    return (
      <div className="flex items-center space-x-2 text-red-600">
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
        <span>无法获取LibreOffice状态</span>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm"
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-gray-900">LibreOffice 状态</h3>
        <div
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            info.available
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {info.available ? "可用" : "不可用"}
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">运行模式:</span>
          <span
            className={`text-sm font-medium ${
              info.mode === "embedded" ? "text-blue-600" : "text-orange-600"
            }`}
          >
            {info.mode === "embedded" ? "嵌入式" : "系统安装"}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">版本:</span>
          <span className="text-sm font-medium text-gray-900">
            {info.version}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">路径:</span>
          <span
            className="text-sm text-gray-500 truncate max-w-48"
            title={info.path}
          >
            {info.path}
          </span>
        </div>
      </div>

      {info.mode === "embedded" && (
        <div className="mt-3 p-2 bg-blue-50 rounded-md">
          <div className="flex items-center space-x-2">
            <svg
              className="w-4 h-4 text-blue-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-sm text-blue-700">
              使用嵌入式LibreOffice，无需手动安装
            </span>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default LibreOfficeEmbeddedStatus;
