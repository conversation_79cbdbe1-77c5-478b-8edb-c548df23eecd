import { isRemoteUrl } from "@/main/utils/path";
import type { FileInfo } from "@/types";
import { useCallback } from "react";
import { accurateBasicByFilePath } from "../service/baiduOCR";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import { setFileTextLoading, updateFileText } from "../store/slice/file";
import { getUrlExtension } from "../utils/file";
import { wxmsgRequest } from "../utils/request/factory";

const useFileAnalysis = () => {
  const dispatch = useAppDispatch();
  /** 文件文本缓存 */
  const fileTextMap = useAppSelector(state => state.files?.fileTextMap);

  /**
   * 获取文件buffer
   * @param filePath 文件路径
   * @returns 返回文件buffer
   */
  const getFileBuffer = async (filePath: string) => {
    const isRemote = isRemoteUrl(filePath);
    if (isRemote) {
      return await wxmsgRequest.get<Buffer<ArrayBufferLike>>(filePath, {
        responseType: "arraybuffer",
      });
    }
    return filePath;
  };

  /**
   * 每次点击-获取文本
   * @param file
   */
  const fileAnalysis = useCallback(
    async (file: FileInfo, checked: boolean) => {
      let text = "";
      const cache = fileTextMap?.find(item => item.id === file.id);

      if (cache && cache.text) {
        dispatch(updateFileText({ id: file.id, selected: checked }));
        return cache.text;
      }
      // 设置文件加载状态
      dispatch(setFileTextLoading({ id: file.id, loading: true }));

      // 获取文件扩展名
      const extension = "." + getUrlExtension(file.path);

      switch (extension) {
        case ".jpg":
        case ".jpeg":
        case ".png": {
          const res = await accurateBasicByFilePath(file.path);
          text = res.words_result.map(item => item.words).join("");
          break;
        }
        case ".pdf": {
          const buffer = await getFileBuffer(file.path);
          const res = await window.electronAPI.usb.readPdfFile(buffer);
          text = res.text;
          break;
        }
        case ".docx": {
          const buffer = await getFileBuffer(file.path);
          const res = await window.electronAPI.usb.readDocxFile(buffer);
          text = res.value;
          break;
        }
      }
      // 首次获取文本
      dispatch(updateFileText({ id: file.id, text, selected: true }));
      return text;
    },
    [dispatch, fileTextMap]
  );

  return {
    fileAnalysis,
    fileTextMap,
  };
};

export default useFileAnalysis;
