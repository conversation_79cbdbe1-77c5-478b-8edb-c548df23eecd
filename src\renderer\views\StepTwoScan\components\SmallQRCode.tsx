import QRCode from "@/renderer/components/QRCode";
import chassis from "@assets/step-two-scan/chassis.png";
import { AnimatePresence } from "motion/react";

interface SmallQRCodeProps {
  // 二维码内容
  value: string;
  // 是否打开
  open: boolean;
  // 点击事件
  onClose: () => void;
}

const SmallQRCode = ({ value, open, onClose }: SmallQRCodeProps) => {
  return (
    <AnimatePresence>
      {open && (
        <div
          className="absolute flex justify-center bottom-[.57vw] left-[1.77vw] w-[7.08vw] cursor-pointer"
          onClick={onClose}
        >
          <img src={chassis} alt="" className="w-full h-max object-contain" />
          {/* 二维码贴图位置 */}
          <div className="absolute bottom-[40%] left-1/2 -translate-x-1/2 w-[5.5vw] h-[5.5vw]">
            <QRCode value={value} />
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default SmallQRCode;
