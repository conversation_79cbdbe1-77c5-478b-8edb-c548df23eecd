import MaxLimit from "@/renderer/components/MaxLimit";
import useFileAnalysis from "@/renderer/hooks/useFileAnalysis";
import { useAppDispatch, useAppSelector } from "@/renderer/store/hooks";
import { addFile } from "@/renderer/store/slice/file";
import { OCXVideoDispMode } from "@/types/services/ocxService";
import camera from "@assets/step-two/camera.png";
import { motion } from "motion/react";
import { useEffect, useState } from "react";

const Camera: React.FC = () => {
  const dispatch = useAppDispatch();
  const { fileAnalysis } = useFileAnalysis();
  const [base64, setBase64] = useState("");
  const files = useAppSelector(state => state.files?.items);
  const [maxLimitVisible, setMaxLimitVisible] = useState(false);

  useEffect(() => {
    // 开始拍摄
    window.electronAPI.ocx.startPlay();
    window.electronAPI.ocx.anyCommand({
      type: "vSetRotate",
      angle: 180,
    });

    window.electronAPI.ocx.setVideoDispMode(OCXVideoDispMode.Default);

    // 监听图片流
    window.electronAPI.ocx.onImageStream(data => {
      setBase64("data:image/jpeg;base64," + data.data);
    });

    return () => {
      // 停止拍摄
      window.electronAPI.ocx.stopPlay();
      // todo：点击上一步或者在第四步关闭时清空文件夹
      // window.electronAPI.ocx.clearFolder();
    };
  }, []);

  const handleTakePhoto = async () => {
    if (files?.length && files.length >= 10) {
      setMaxLimitVisible(true);
      return;
    }

    const file = await window.electronAPI.ocx.takePhoto("JPG");
    if (file) {
      fileAnalysis(file, true);
      dispatch(addFile(file));
    }
  };

  return (
    <div className="w-full h-full relative">
      {/* 文件数量限制提示 */}
      <MaxLimit
        visible={maxLimitVisible}
        maxCount={10}
        onClose={() => {
          setMaxLimitVisible(false);
        }}
      />

      <div className="absolute top-0 left-0 w-full h-[3.75vw] flex justify-center items-center bg-white/10 font-ZQKLKT-Bold text-[1.67vw] text-white">
        拍摄实时画面，请调整至合适位置
      </div>

      {/* 高拍仪画面 */}
      {base64 && (
        <img
          src={base64}
          alt="camera"
          draggable={false}
          className="w-full h-full"
        />
      )}

      <div className="absolute bottom-[.8vw] w-full h-max flex justify-center items-center">
        <motion.img
          src={camera}
          alt="camera"
          className="w-[4.27vw] object-contain cursor-pointer"
          whileTap={{ scale: 0.9 }}
          onClick={handleTakePhoto}
        />
      </div>
    </div>
  );
};

export default Camera;
