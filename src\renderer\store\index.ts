import { combineReducers, configureStore } from "@reduxjs/toolkit";
import {
  FLUSH,
  PAUSE,
  PERSIST,
  persistReducer,
  persistStore,
  PURGE,
  REGISTER,
  REHYDRATE,
} from "redux-persist";
import storage from "redux-persist/lib/storage";
import clientReducer from "./slice/client";
import disputeReducer from "./slice/disputeSlice";
import filesReducer from "./slice/file";
import usbReducer from "./slice/usb";
import userReducer from "./slice/user";

const rootReducer = combineReducers({
  files: filesReducer,
  dispute: disputeReducer,
  usb: usbReducer,
  user: userReducer,
  client: clientReducer,
});

const persistedReducer = persistReducer(
  {
    key: "root",
    storage: storage,
    whitelist: ["client"],
  },
  rootReducer
);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        ignoredPaths: [],
      },
    }),
});

export const persistor = persistStore(store);

// Infer the `RootState`,  `AppDispatch`, and `AppStore` types from the store itself
export type RootState = ReturnType<typeof persistedReducer>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;
export type AppStore = typeof store;
