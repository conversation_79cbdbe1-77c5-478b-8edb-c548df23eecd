import { CardService } from "@/renderer/devices/card";
import { useEmptyPrint } from "@/renderer/hooks";
import useSubmitCount from "@/renderer/hooks/useSubmitCount";
import idCard from "@assets/common/id-card.png";
import { motion } from "motion/react";
import type React from "react";
import { useEffect } from "react";
import { twMerge } from "tailwind-merge";

type Props = {
  className?: string;
  onBack?: () => void;
};

const IdCardSensor: React.FC<Props> = ({
  className = "w-[26vw] text-[1.5vw]",
  onBack,
}) => {
  const { submitCount } = useSubmitCount();
  const { emptyPrint } = useEmptyPrint();

  useEffect(() => {
    const cardService = new CardService();
    cardService.startContinuousRead("USB", "USB1", res => {
      if (res.IDNumber) {
        cardService.stopContinuousRead("USB", "USB1");

        // 提交打印次数
        submitCount({
          type: "print",
          user: res.IDNumber,
        });

        // TODO：触发空模板打印
        emptyPrint();
        onBack?.();
      }
    });

    return () => {
      cardService.disconnect();
    };
  }, [submitCount, onBack, emptyPrint]);

  return (
    <motion.div
      initial={{ opacity: 0, x: -100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 100 }}
      transition={{ duration: 0.3 }}
      className={twMerge(
        "flex flex-col items-center justify-center gap-[2vw]",
        className
      )}
    >
      <img
        src={idCard}
        alt="idCard"
        className="object-contain aspect-[24/14] px-[1vw]"
        draggable={false}
      />
      <div className="text-gradient">请将身份证放置在标识感应区进行扫描</div>
    </motion.div>
  );
};

export default IdCardSensor;
