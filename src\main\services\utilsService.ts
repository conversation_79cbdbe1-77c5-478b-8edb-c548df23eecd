import { IPC_CHANNELS } from "@/shared/constants";
import type { PrintJob } from "@/types/services/utilService";
import { exec } from "child_process";
import { app, dialog } from "electron";
import * as fs from "fs";
import https from "https";
import * as mammoth from "mammoth";
import os from "os";
import path from "path";
import { getDefaultPrinter, print } from "pdf-to-printer";
import { ipcManager } from "../ipc/IpcManager";
import { getReadableUniqueFileName } from "../utils/file";

class UtilsService {
  private static instance: UtilsService;

  private constructor() {
    this.setupIpcHandlers();
  }

  public static getInstance(): UtilsService {
    if (!UtilsService.instance) {
      UtilsService.instance = new UtilsService();
    }
    return UtilsService.instance;
  }

  private setupIpcHandlers() {
    // 读取文件为base64
    ipcManager.registerHandler(
      IPC_CHANNELS.UTIL.READ_FILE_AS_BASE64,
      async (_, filePath: string) => {
        return await this.readFileAsBase64(filePath);
      }
    );

    // 读取文件
    ipcManager.registerHandler(
      IPC_CHANNELS.UTIL.READ_FILE_AS_BUFFER,
      async (_, filePath: string) => {
        return await this.readFileAsBuffer(filePath);
      }
    );

    ipcManager.registerHandler(
      IPC_CHANNELS.UTIL.FETCH_JSON_FROM_URL,
      async (_, url: string) => await this.fetchJsonFromUrl(url)
    );

    ipcManager.registerHandler(
      IPC_CHANNELS.UTIL.CONVERT_WORD_TO_HTML,
      async (_, wordFilePath: string) =>
        await this.convertWordToHtml(wordFilePath)
    );

    ipcManager.registerHandler(
      IPC_CHANNELS.UTIL.SAVE_FILE_TO_TEMP,
      async (_, data: { buffer: ArrayBuffer; fileName: string }) => {
        return await this.saveFileToTemp(data.buffer, data.fileName);
      }
    );

    ipcManager.registerHandler(
      IPC_CHANNELS.UTIL.SAVE_FILE_TO_PATH,
      async (_, data: { buffer: ArrayBuffer; defaultPath?: string }) => {
        return await this.saveFileToPath(data.buffer, data.defaultPath);
      }
    );

    ipcManager.registerHandler(
      IPC_CHANNELS.UTIL.PRINT,
      async (event, html, duplex, pdfPage) => {
        return await this.handlePrint(event, html, duplex, pdfPage);
      }
    );
  }

  private async saveFileToTemp(buffer: ArrayBuffer, fileName: string) {
    try {
      const tempDir = path.join(process.cwd(), "temp", "downloads");
      // 创建临时文件夹
      await fs.promises.mkdir(tempDir, {
        // 如果文件夹不存在，则创建
        recursive: true,
      });

      const { ext, name } = path.parse(fileName);
      const uniqueName = getReadableUniqueFileName(name);
      const uniqueFullName = `${uniqueName}${ext}`;
      const filePath = path.join(tempDir, uniqueFullName);
      await fs.promises.writeFile(filePath, Buffer.from(buffer));

      return filePath;
    } catch (error) {
      console.error("保存文件失败：", error);
      return null;
    }
  }

  /**
   * 读取文件为base64
   * @param filePath 文件路径
   * @returns 文件内容
   */
  private async readFileAsBase64(filePath: string) {
    try {
      return fs.readFileSync(filePath).toString("base64");
    } catch (error) {
      console.error("读取文件内容失败:", error);
      throw error;
    }
  }

  /**
   * 读取文件
   * @param filePath 文件路径
   * @returns 文件内容
   */
  private async readFileAsBuffer(filePath: string) {
    try {
      return await fs.promises.readFile(filePath);
    } catch (error) {
      console.error("读取文件内容失败:", error);
      throw error;
    }
  }

  /**
   * 从url获取json
   * @param url 链接
   * @returns 返回json
   */
  private async fetchJsonFromUrl(url: string) {
    return new Promise((resolve, reject) => {
      https
        .get(url, res => {
          let data = "";
          res.on("data", chunk => {
            data += chunk;
          });
          res.on("end", () => {
            try {
              resolve(JSON.parse(data));
            } catch (err) {
              reject(err);
            }
          });
          res.on("error", err => {
            reject(err);
          });
        })
        .on("error", err => {
          reject(err);
        });
    });
  }

  /**
   * 将word文件转换为html
   * @param wordFilePath word文件路径
   * @returns 返回html
   */
  private async convertWordToHtml(wordFilePath: string) {
    try {
      // 定义样式映射，将Word样式转换为CSS样式
      const styleMap = `
        p[style-name='Heading 1'] => h1:fresh
        p[style-name='Heading 2'] => h2:fresh
        p[style-name='Heading 3'] => h3:fresh
        p[style-name='Heading 4'] => h4:fresh
        p[style-name='Heading 5'] => h5:fresh
        p[style-name='Heading 6'] => h6:fresh
        p[style-name='Title'] => h1.title:fresh
        p[style-name='Subtitle'] => h2.subtitle:fresh
        p[style-name='Quote'] => blockquote:fresh
        p[style-name='Intense Quote'] => blockquote.intense:fresh
        p[style-name='List Paragraph'] => li:fresh
        p[style-name='Footnote Text'] => p.footnote:fresh
        r[style-name='Strong'] => strong
        r[style-name='Emphasis'] => em
        r[style-name='Code'] => code
        table => table.table:fresh
        tr => tr:fresh
        td => td:fresh
        th => th:fresh
      `;

      // 转换选项配置
      const options = {
        path: wordFilePath,
        styleMap: styleMap,
        includeDefaultStyleMap: true,
        includeEmbeddedStyleMap: true,
        idPrefix: "docx-",
        transformDocument: (document: any) => {
          // 可以在这里添加自定义的文档转换逻辑
          return document;
        },
      };

      const result = await mammoth.convertToHtml(options);

      // 返回带有样式的HTML
      return result.value;
    } catch (err) {
      console.error("Word转HTML失败:", err);
      return null;
    }
  }

  /**
   * 保存到指定位置
   * @param buffer 缓冲区
   * @param filePath 文件路径
   * @returns 返回文件路径
   */
  private async saveFileToPath(buffer: ArrayBuffer, defaultPath?: string) {
    // 获取桌面路径
    const desktopPath = path.join(os.homedir(), "Desktop", defaultPath || "");

    const result = await dialog.showSaveDialog({
      title: "保存文件",
      defaultPath: defaultPath || desktopPath,
      filters: [{ name: "Word", extensions: ["docx"] }],
    });

    if (result.canceled) return null;

    await fs.promises.writeFile(result.filePath, Buffer.from(buffer));

    return result.filePath;
  }

  /**
   * 等待获取任务提交
   * @param printerName
   * @param timeout
   * @param interval
   * @returns
   */
  private async waitForPrintJobStart(
    printerName: string,
    timeout = 10_000,
    interval = 100
  ): Promise<boolean> {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      const ids = await this.getPrintJob(printerName);
      if (ids.length > 0) return true;
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    return false;
  }

  // private async waitForPrintJobDone(
  //   printName: string,
  //   timeout = 30_000,
  //   interval = 1000
  // ): Promise<boolean> {
  //   const startTime = Date.now();
  //   while (Date.now() - startTime < timeout) {
  //     const ids = await this.getPrintJob();
  //     if (ids.length === 0) return true; // 打印任务清空
  //     await new Promise(resolve => setTimeout(resolve, interval));
  //   }
  //   return false;
  // }

  /**
   * 获取打印任务
   * @param printName 打印机名称
   * @returns
   */
  private async getPrintJob(printerName: string): Promise<PrintJob[]> {
    return new Promise((resolve, reject) => {
      // ========== 脚本内容
      /**
       * param(
       * [string]$PrinterName = "Brother HL-2260D Printer"
       * )
       * Get-PrintJob -PrinterName $PrinterName | Select-Object * | ConvertTo-Json -Depth 5
       */

      const scriptPath = path.join(
        app.getAppPath(),
        "resources/scripts/get-print-jobs.ps1"
      );

      const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -PrinterName "${printerName}"`;

      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.error("执行 PowerShell 脚本出错:", error);
          return;
        }

        if (stderr) {
          console.warn("PowerShell 脚本警告/错误信息:", stderr);
        }

        if (!stdout.trim()) {
          return resolve([]);
        }
        try {
          const jobs = JSON.parse(stdout);
          resolve(Array.isArray(jobs) ? jobs : [jobs]);
        } catch (e) {
          reject(error);
        }
      });

      // const ps = spawn("powerShell.exe", [
      //   "-command",
      //   `Get-PrintJob -PrinterName '${printName}' | Select-Object -ExpandProperty ID`,
      // ]);

      // let output = "";

      // ps.stdout.on("data", data => {
      //   console.log("🚀 ~ UtilsService ~ returnnewPromise ~ data:", data);
      //   output += data.toString();
      // });
      // ps.stderr.on("data", data => {
      //   console.error("stderr", data);
      // });

      // ps.on("close", () => {
      //   const ids = output
      //     .split(/\r?\n/)
      //     .map(s => parseInt(s.trim()))
      //     .filter(n => !isNaN(n));
      //   resolve(ids);
      // });
    });
  }

  /**
   * 打印PDF文件
   * @param pdfPath PDF文件路径
   * @param duplex 双面打印
   * @param pdfPage pdf页数
   * @param printerName 打印机名称
   * @returns Promise<boolean>
   */
  async handlePrint(
    event: Electron.IpcMainInvokeEvent,
    pdfPath: string,
    duplex: boolean,
    pdfPage: number,
    printerName?: string
  ) {
    try {
      // 验证PDF文件是否存在
      if (!fs.existsSync(pdfPath)) {
        throw new Error(`PDF文件不存在: ${pdfPath}`);
      }

      // 验证文件扩展名
      if (path.extname(pdfPath).toLowerCase() !== ".pdf") {
        throw new Error(`文件不是PDF格式: ${pdfPath}`);
      }

      event.sender.send(IPC_CHANNELS.UTIL.PRINT_STATUS, {
        status: 0,
      });

      // 默认打印机
      const printer = await getDefaultPrinter();

      const printers = printerName || printer?.name;

      if (!printers) {
        event.sender.send(IPC_CHANNELS.UTIL.PRINT_STATUS, {
          status: -1,
          error: "打印机不存在",
        });
        return;
      }

      // 开始打印文件
      await print(pdfPath, {
        silent: true,
        side: duplex ? "duplexlong" : "simplex",
        printer: printers,
      });
      // const started = await this.waitForPrintJobStart(printers, 10_000);
      // 等待打印机启动
      await new Promise(resolve => setTimeout(resolve, 8_000));

      // 模拟打印时间
      await new Promise(resolve =>
        setTimeout(resolve, 1_000 * (duplex ? 4 : 2) * pdfPage)
      );

      // if (!started) {
      //   event.sender.send(IPC_CHANNELS.UTIL.PRINT_STATUS, {
      //     status: -1,
      //     error: "任务未进入队列",
      //   });
      //   return;
      // }

      event.sender.send(IPC_CHANNELS.UTIL.PRINT_STATUS, {
        status: 1,
      });
    } catch (error) {
      event.sender.send(IPC_CHANNELS.UTIL.PRINT_STATUS, {
        status: -1,
      });
    }
  }
}

export default UtilsService;
