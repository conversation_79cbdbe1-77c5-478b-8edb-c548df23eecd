import maxLimit from "@assets/step-two/max-limit.png";
import { AnimatePresence, motion } from "motion/react";
import React, { useEffect, useRef } from "react";
import { twMerge } from "tailwind-merge";

const MaxLimit: React.FC<{
  className?: string;
  /** 文件上限数量 */
  maxCount?: number;
  /** 是否显示组件 */
  visible?: boolean;
  /** 点击关闭按钮 */
  onClose?: () => void;
}> = ({ className, maxCount = 9, visible = true, onClose }) => {
  const timer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    timer.current = setTimeout(() => {
      onClose?.();
    }, 3000);

    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
      }
    };
  }, [visible]);

  return (
    <AnimatePresence>
      {visible && (
        <div className="absolute left-0 top-0 w-full h-full z-[2] flex items-center justify-center">
          <motion.div
            className={twMerge(
              "relative w-full h-[3.75vw] bg-contain bg-no-repeat bg-center",
              className
            )}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{
              duration: 0.3,
              ease: "easeOut",
            }}
            style={{
              backgroundImage: `url(${maxLimit})`,
            }}
            onClick={onClose}
          >
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-[55%] text-white text-[1.35vw] whitespace-nowrap">
              文件上限数量为{maxCount}，请酌情删除
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default MaxLimit;
