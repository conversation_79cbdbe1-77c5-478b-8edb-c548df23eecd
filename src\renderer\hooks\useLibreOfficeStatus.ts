import LibreOfficeUtils from "@/renderer/utils/libreOffice";
import { useCallback, useEffect, useState } from "react";

/**
 * LibreOffice 状态管理 Hook
 */
export const useLibreOfficeStatus = () => {
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheckTime, setLastCheckTime] = useState<number | null>(null);

  /**
   * 检查 LibreOffice 可用性
   */
  const checkAvailability = useCallback(
    async (force = false) => {
      // 如果正在检查，避免重复检查
      if (isChecking && !force) return;

      // 如果不是强制检查，且上次检查时间在5分钟内，直接返回缓存结果
      if (
        !force &&
        lastCheckTime &&
        Date.now() - lastCheckTime < 5 * 60 * 1000
      ) {
        return;
      }

      setIsChecking(true);
      try {
        const libreOfficeUtils = LibreOfficeUtils.getInstance();
        const available = await libreOfficeUtils.checkAvailability();
        setIsAvailable(available);
        setLastCheckTime(Date.now());

        console.log("LibreOffice 状态检查完成:", available);
      } catch (error) {
        console.error("LibreOffice 状态检查失败:", error);
        setIsAvailable(false);
      } finally {
        setIsChecking(false);
      }
    },
    [isChecking, lastCheckTime]
  );

  /**
   * 强制重新检查
   */
  const refreshStatus = () => {
    checkAvailability(true);
  };

  useEffect(() => {
    // 组件挂载时检查状态
    checkAvailability();
  }, [checkAvailability]);

  return {
    isAvailable,
    isChecking,
    lastCheckTime,
    checkAvailability,
    refreshStatus,
  };
};
