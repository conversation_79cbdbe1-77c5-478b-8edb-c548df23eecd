import type { UsbFileInfo } from "@/types";
import {
  DragEndEvent,
  DragStartEvent,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { useState } from "react";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import { reorderFiles } from "../store/slice/file";

/**
 * 拖拽排序
 */
export const useDragSort = () => {
  const dispatch = useAppDispatch();
  const files = useAppSelector(state => state.files?.items);
  const [activeFile, setActiveFile] = useState<UsbFileInfo | null>(null);

  // 配置传感器，支持鼠标和触摸操作
  const sensors = useSensors(
    // 鼠标传感器
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    // 触摸传感器
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200,
        tolerance: 5,
      },
    })
  );

  /**
   * 处理拖拽开始事件
   * 设置当前拖拽的文件项
   */
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const file = files?.find(f => f.id === active.id);
    setActiveFile(file || null);
  };

  /**
   * 处理拖拽结束事件
   * 执行文件重新排序并清除拖拽状态
   */
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveFile(null);

    // 如果没有目标位置或者位置没有改变，则不执行排序
    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = files?.findIndex(f => f.id === active.id) || -1;
    const newIndex = files?.findIndex(f => f.id === over.id) || -1;

    // 确保索引有效后执行排序
    if (oldIndex !== -1 && newIndex !== -1) {
      dispatch(reorderFiles({ fromIndex: oldIndex, toIndex: newIndex }));
    }
  };

  /**
   * 获取当前拖拽文件在列表中的位置索引
   */
  const getActiveFileIndex = () => {
    if (!activeFile) return -1;
    const index = files?.findIndex(f => f.id === activeFile.id) || -1;
    return index + 1;
  };

  return {
    // 数据
    files: files || [],
    activeFile,

    // 拖拽配置
    sensors,

    // 事件处理器
    handleDragStart,
    handleDragEnd,

    // 工具函数
    getActiveFileIndex,

    // 拖拽项ID列表
    sortableItems: files?.map(f => f.id) || [],
  };
};
