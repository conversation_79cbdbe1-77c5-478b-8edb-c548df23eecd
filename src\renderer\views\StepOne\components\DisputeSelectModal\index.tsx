import Close from "@/renderer/components/Close";
import LoadingSpinner from "@/renderer/components/LoadingSpinner";
import { useAppDispatch } from "@/renderer/store/hooks";
import { setEnterType } from "@/renderer/store/slice/client";
import { DisputePageType, DisputeType } from "@/types/views/dispute";
import printBg from "@assets/step-one/print-bg.png";
import uploadBg from "@assets/step-one/upload-bg.png";
import { AnimatePresence, motion } from "motion/react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import ButtonWithImage, { type ButtonType } from "./ButtonWithImage";
import EmptyPrintSelect from "./EmptyPrintSelect";
import ChangePrint from "./EmptyPrintSelect/ChangePrint";
import PageCard from "./PageCard";
import SplitLine from "./SplitLine";
import TrackBg from "./TrackBg";

type Props = {
  open: boolean;
  // 是否正在加载
  loading?: boolean;
  onClose: () => void;
  /** 纠纷表单类型改变时触发 */
  onDisputePageChange: (type: DisputePageType) => void;
  /** 当前选中的纠纷类型代码 */
  disputeTypeCode?: DisputeType;
};

const DisputeSelectModal: React.FC<Props> = ({
  open,
  loading,
  onClose,
  disputeTypeCode,
  onDisputePageChange,
}) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  /** 页面类型 */
  const [pageType, setPageType] = useState<DisputePageType>(
    DisputePageType.LAWSUIT
  );
  /** 选中按钮 */
  const [selectedButton, setSelectedButton] = useState<ButtonType>();
  /** 选中按钮索引 */
  const [selectedButtonIndex, setSelectedButtonIndex] = useState<number>(-1);

  const buttonList: { type: ButtonType; visible: boolean }[] = [
    { type: "empty", visible: true },
    { type: "manual", visible: true },
    { type: "upload", visible: true },
    { type: "local", visible: false },
    { type: "scan", visible: true },
  ];

  const realButtonList = buttonList.filter(item => item.visible);

  /** 点击按钮时，更新选中按钮 */
  const handleButtonClick = (index: number, type: ButtonType) => {
    reset();
    setSelectedButtonIndex(index);
    setSelectedButton(type);
    switch (type) {
      case "empty":
        dispatch(setEnterType("empty"));
        break;
      case "manual":
        dispatch(setEnterType("manual"));
        navigate("/dispute/step-four");
        break;
      case "upload":
        dispatch(setEnterType("upload"));
        navigate("/dispute/step-two");
        break;
      case "scan":
        dispatch(setEnterType("scan"));
        navigate("/dispute/step-two-scan");
        break;
    }
  };

  const reset = () => {
    setPrintType(undefined);
    setSelectedButton(undefined);
    setSelectedButtonIndex(-1);
  };

  /** 切换页面时，重置选中按钮 */
  const handlePageCardClick = (type: DisputePageType) => {
    reset();
    setPageType(type);
    onDisputePageChange(type);
  };

  /** 关闭弹窗 */
  const handleClose = () => {
    if (selectedButton) {
      reset();
    } else {
      onClose();
    }
  };

  /**
   * =========================================
   * 空模版打印
   * =========================================
   */
  const [printType, setPrintType] = useState<"card" | "pass" | undefined>();

  const handleCardPrint = () => {
    setPrintType("card");
  };

  const handlePassPrint = () => {
    setPrintType("pass");
  };

  // 回到纠纷类型页面
  const handleBack = () => {
    reset();
    onClose();
    navigate("/dispute/step-one");
  };

  // todo: 预加载图片
  useEffect(() => {
    const imgs = [printBg, uploadBg];
    imgs.forEach(src => {
      const img = new window.Image();
      img.src = src;
    });
  }, []);

  return (
    <AnimatePresence>
      {open && (
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.5 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="fixed z-10 top-0 left-0 w-full h-full pointer-events-auto flex items-center justify-center"
        >
          <div
            className="relative w-[61.46vw] h-[40.9vw] overflow-hidden"
            style={{
              backgroundImage: `url(${selectedButton === "empty" ? printBg : uploadBg})`,
              backgroundSize: "100% 100%",
            }}
          >
            {/* 关闭按钮 */}
            <Close
              onClick={handleClose}
              className="w-[2.9vw] h-[2.9vw] absolute top-[.89vw] right-[.89vw] cursor-pointer"
            />
            <LoadingSpinner
              show={loading}
              className="absolute left-0 top-0 right-0 bottom-0"
            />

            {loading || (
              <>
                {/* 空模版打印 切换方式 */}
                {printType && (
                  <ChangePrint
                    type={printType}
                    onChange={type => setPrintType(type)}
                  />
                )}

                {/* 空模版打印选择 */}
                {selectedButton === "empty" && (
                  <EmptyPrintSelect
                    printType={printType}
                    onCardPrint={handleCardPrint}
                    onPassPrint={handlePassPrint}
                    onBack={handleBack}
                  />
                )}

                {!selectedButton && (
                  <PageCard
                    active={pageType}
                    onClick={handlePageCardClick}
                    disputeTypeCode={disputeTypeCode}
                  />
                )}

                <div className="w-[calc(100%-.6vw)] left-1/2 transform -translate-x-1/2 h-[7.45vw] flex items-center justify-evenly absolute bottom-0">
                  {realButtonList.map((item, index) => (
                    <React.Fragment key={index}>
                      <ButtonWithImage
                        type={item.type}
                        onClick={() => handleButtonClick(index, item.type)}
                      />
                      {index !== realButtonList.length - 1 && <SplitLine />}
                    </React.Fragment>
                  ))}
                  <TrackBg
                    count={realButtonList.length}
                    index={selectedButtonIndex}
                  />
                </div>
              </>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DisputeSelectModal;
