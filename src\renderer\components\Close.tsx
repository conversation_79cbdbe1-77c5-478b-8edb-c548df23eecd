import { X } from "lucide-react";
import { motion } from "motion/react";
import type React from "react";
import { twMerge } from "tailwind-merge";

interface CloseProps {
  className?: string;
  onClick: () => void;
}

const Close: React.FC<CloseProps> = ({ onClick, className }) => {
  const classes = twMerge(
    "w-[2.92vw] h-[2.92vw] rounded-full bg-white/10 flex justify-center items-center cursor-pointer p-[.5vw]",
    className
  );

  return (
    <motion.div
      className={classes}
      whileTap={{ scale: 0.9 }}
      whileHover={{ scale: 0.9 }}
      onClick={onClick}
    >
      <X className="w-full h-full" color="white" />
    </motion.div>
  );
};

export default Close;
