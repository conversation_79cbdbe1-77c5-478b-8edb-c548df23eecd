import { useFormatDate } from "@/renderer/hooks";
import useTitle from "@/renderer/hooks/useTitle";
import { HomeType as HomeTypeEnum } from "@/types/views/home";
import china from "@assets/common/badge-courts.png";
import administrative from "@assets/home/<USER>";
import BG from "@assets/home/<USER>";
import civil from "@assets/home/<USER>";
import commercial from "@assets/home/<USER>";
import criminal from "@assets/home/<USER>";
import enforcement from "@assets/home/<USER>";
import environmental from "@assets/home/<USER>";
import intellectual from "@assets/home/<USER>";
import maritime from "@assets/home/<USER>";
import mediate from "@assets/home/<USER>";
import stateCompensation from "@assets/home/<USER>";
import { useNavigate } from "react-router-dom";
import HomeType from "./components/HomeType";

const typeList = [
  {
    id: HomeTypeEnum.Criminal,
    name: "刑事",
    img: criminal,
  },
  {
    id: HomeTypeEnum.IntellectualProperty,
    name: "知识产权",
    img: intellectual,
  },
  {
    id: HomeTypeEnum.Environmental,
    name: "环境资源",
    img: environmental,
  },
  {
    id: HomeTypeEnum.StateCompensation,
    name: "国家赔偿",
    img: stateCompensation,
  },
  {
    id: HomeTypeEnum.Mediate,
    name: "调解",
    img: mediate,
  },
  {
    id: HomeTypeEnum.Civil,
    name: "民事",
    img: civil,
  },
  {
    id: HomeTypeEnum.Maritime,
    name: "海事",
    img: maritime,
  },
  {
    id: HomeTypeEnum.Commercial,
    name: "商事",
    img: commercial,
  },
  {
    id: HomeTypeEnum.Administrative,
    name: "行政",
    img: administrative,
  },
  {
    id: HomeTypeEnum.Enforcement,
    name: "执行",
    img: enforcement,
  },
];

const Home = () => {
  const navigate = useNavigate();
  const title = useTitle();

  const { dateStr, weekdayStr, hour, minute, second } = useFormatDate();

  const handleClick = (id: HomeTypeEnum) => {
    switch (id) {
      case HomeTypeEnum.Civil:
        navigate("/dispute/step-one");
        break;
      default:
        console.log(id, "开发中");
    }
  };

  return (
    <div
      className="h-screen aspect-vi deo bg-cover bg-center bg-no-repeat relative flex flex-col overflow-hidden"
      style={{ backgroundImage: `url(${BG})`, backgroundSize: "100% 100%" }}
    >
      <div className="absolute top-[2.44vw] px-[1.92vw] flex justify-between items-center w-full text-[#CCDEFF] text-[1.04vw]">
        {/* 标题区域 */}
        <div className="flex items-center gap-[5px]">
          <img src={china} alt="china" className="w-[1.3vw] h-[1.45vw]" />
          <div>{title}</div>
        </div>
        {/* 时间区域 */}
        <div className="flex items-center gap-[1.5vw] font-ZQKLKT-Bold">
          <div className="flex items-center gap-[.75vw]">
            <span>{dateStr}</span>
            <div className="w-[5.5vw]">
              {hour}:{minute}:{second}
            </div>
          </div>
          <div className="text-[1.04vw]">{weekdayStr}</div>
        </div>
      </div>

      {/* 类型选择区域、居中 */}
      <div className="h-full flex items-center justify-center">
        <HomeType typeList={typeList} onClick={handleClick} />
      </div>
    </div>
  );
};

export default Home;
