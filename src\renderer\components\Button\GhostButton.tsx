import { motion } from "motion/react";
import type React from "react";
import { twMerge } from "tailwind-merge";

interface Props {
  className?: string;
  onClick?: () => void;
  children: React.ReactNode;
}

const GhostButton: React.FC<Props> = ({ onClick, children, className }) => {
  return (
    <motion.div
      className={twMerge(
        "w-max h-max py-[0.73vw] px-[2.45vw] rounded-full text-white text-[1.25vw] leading-none cursor-pointer border border-white/50 bg-transparent shadow-sm",
        className
      )}
      onClick={onClick}
      whileTap={{
        scale: 0.95,
      }}
    >
      {children}
    </motion.div>
  );
};

export default GhostButton;
