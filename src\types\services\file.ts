export type FileType =
  | "pdf"
  | "word"
  | "excel"
  | "ppt"
  | "txt"
  | "image"
  | "other";

export interface FileInfo {
  /** 文件ID */
  id: string;
  /** 文件名称 */
  name: string;
  /** 文件路径 */
  path: string;
  /** 文件大小 */
  size?: number;
  /** 文件类型 */
  type: FileType;
  /** 文件扩展名 */
  extension?: string;
  /** 文件修改时间 */
  lastModified?: string;
  /** 是否被选中 */
  selected?: boolean;
  /** 是否为远程文件 */
  remote?: "usb" | "ocx" | boolean;
}
