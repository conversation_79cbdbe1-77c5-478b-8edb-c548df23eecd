import type { PrintStatus } from "@/types/services/utilService";

export interface UtilAPI {
  /**
   * 读取文件为base64
   * @param filePath 文件路径
   * @returns 返回文件内容
   */
  readFileAsBase64: (filePath: string) => Promise<string>;
  /**
   * 读取文件为 Buffer
   * @param filePath 文件路径
   * @returns 文件内容 Buffer
   */
  readFileAsBuffer: (filePath: string) => Promise<Buffer>;
  /**
   * 从url获取json
   * @param url 链接
   * @returns 返回json
   */
  fetchJsonFromUrl: <T>(url: string) => Promise<T | null>;
  /**
   * 将word文件转换为html
   * @param wordFilePath word文件路径
   * @returns 返回html
   */
  convertWordToHtml: (wordFilePath: string) => Promise<string | null>;
  /**
   * 将文件保存到临时文件夹
   * @param filePath 文件路径
   * @returns 返回临时文件路径
   */
  saveFileToTemp: (
    buffer: ArrayBuffer,
    fileName: string
  ) => Promise<string | null>;

  /**
   * 将文件保存到指定位置
   * @param buffer 缓冲区
   * @param defaultPath 默认路径
   * @returns 返回文件路径
   */
  saveFileToPath: (
    buffer: ArrayBuffer,
    defaultPath?: string
  ) => Promise<string | null>;

  /**
   * 打印
   * @param pdfPath pdf路径
   * @param duplex 是否双面打印
   * @param pdfPage pdf文件页数
   */
  print: (pdfPath: string, duplex: boolean, pdfPage: number) => Promise<void>;

  /**
   * 监听打印状态（持续监听）
   * @param callback 回调函数，接收打印状态对象
   * @returns 取消监听函数
   */
  onPrintStatus: (callback: (data: PrintStatus) => void) => void;
}
