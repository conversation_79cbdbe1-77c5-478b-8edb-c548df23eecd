import QRCode from "@/renderer/components/QRCode";
import chassis from "@assets/step-two-scan/chassis.png";
import { AnimatePresence } from "motion/react";

interface BigQRCodeProps {
  value: string;
  open: boolean;
}

const BigQRCode = ({ value, open }: BigQRCodeProps) => {
  return (
    <AnimatePresence>
      {open && (
        <div className="flex-1 flex flex-col items-center justify-center">
          {/* 二维码贴图位置 */}
          <div className="relative">
            <div className="relative z-10 w-[18.39vw] h-[18.39vw] ">
              <QRCode value={value} />
            </div>
            <div className="w-[25.73vw] absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-[40%] z-0 ">
              <img src={chassis} alt="" className="w-full object-contain" />
            </div>
          </div>
          <div className="mt-[4.83vw] text-[1.98vw] text-primary-70 whitespace-nowrap">
            请通过手机扫码上传所需的材料
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default BigQRCode;
