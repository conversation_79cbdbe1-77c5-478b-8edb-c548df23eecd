import { useAppSelector } from "@/renderer/store/hooks";
import React from "react";
import { twMerge } from "tailwind-merge";
import Camera from "./Camera";
import UsbArea from "./UsbArea";

const UploadArea: React.FC = () => {
  const uploadType = useAppSelector(state => state.files?.uploadType);

  return (
    <div className="relative w-[31.35vw] h-[calc(100%-.83vw)] mb-[.83vw] overflow-hidden bg-deepOcean-90">
      <Border>{uploadType === "usb" ? <UsbArea /> : <Camera />}</Border>
    </div>
  );
};

export default UploadArea;

// 三角形
const Triangle: React.FC<{
  type: "lt" | "rt" | "lb" | "rb";
  className?: string;
}> = ({ type, className }) => {
  const classes = twMerge(
    "w-0 h-0 border-brightSky-light border-[.52vw]",
    type === "lt" && "border-r-transparent border-b-transparent",
    type === "rt" && "border-l-transparent border-b-transparent",
    type === "lb" && "border-r-transparent border-t-transparent",
    type === "rb" && "border-l-transparent border-t-transparent",
    className
  );

  return <div className={classes}></div>;
};

// 边框
const Border: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return (
    <div className="w-full h-full border border-width-[.2vw] border-[#0964e0]">
      <Triangle type="lt" className="absolute top-0 left-0 z-10" />
      <Triangle type="rt" className="absolute top-0 right-0 z-10" />
      <Triangle type="lb" className="absolute bottom-0 left-0 z-10" />
      <Triangle type="rb" className="absolute bottom-0 right-0 z-10" />

      {children}
    </div>
  );
};
