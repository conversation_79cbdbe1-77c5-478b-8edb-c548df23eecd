import axios, { AxiosInstance, CancelTokenSource } from "axios";
import { DEFAULT_CONFIG } from "./config";
import {
  setupRequestInterceptor,
  setupResponseInterceptor,
} from "./interceptors";
import { RequestConfig, RequestManager } from "./types";

/**
 * 网络请求管理器
 */
class HttpRequestManager implements RequestManager {
  private static instance: HttpRequestManager;
  private axiosInstances: Map<string, AxiosInstance> = new Map();
  private cancelTokenMap: Map<string, CancelTokenSource> = new Map();
  private requestQueue: Set<string> = new Set();

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): HttpRequestManager {
    if (!HttpRequestManager.instance) {
      HttpRequestManager.instance = new HttpRequestManager();
    }
    return HttpRequestManager.instance;
  }

  /**
   * 创建新的请求实例
   *
   * @param name 实例名称
   * @param baseURL 基础URL
   * @returns 实例
   */
  public createInstance(name: string, config?: RequestConfig): AxiosInstance {
    const defaultConfig = {
      ...DEFAULT_CONFIG,
      ...config,
      baseURL: config?.baseURL,
    };

    const instance = axios.create(defaultConfig);

    setupRequestInterceptor(instance);
    setupResponseInterceptor(instance);

    this.axiosInstances.set(name, instance);

    return instance;
  }

  /**
   * 通用请求方法
   */
  public async request<T = any>(config: RequestConfig): Promise<T> {
    const instance = this.getAxiosInstance(config.instanceName || "default");
    const requestId = config.requestId || this.generateRequestId();

    // 创建取消令牌
    const cancelToken = this.createCancelToken(requestId);

    // 添加到请求队列
    this.requestQueue.add(requestId);

    try {
      // 发送请求
      const response = await instance.request<T>({
        ...config,
        cancelToken: cancelToken.token,
      });

      return response as T;
    } finally {
      // 清理请求
      this.cleanup(requestId);
    }
  }

  /**
   * GET请求
   */
  public async get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>({
      ...config,
      method: "GET",
      url,
    });
  }

  /**
   * POST请求
   */
  public async post<T = any>(
    url: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    const res = await this.request<T>({
      ...config,
      method: "POST",
      url,
      data,
    });
    return res;
  }

  /**
   * PUT请求
   */
  public async put<T = any>(
    url: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>({
      ...config,
      method: "PUT",
      url,
      data,
    });
  }

  /**
   * DELETE请求
   */
  public async delete<T = any>(
    url: string,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>({
      ...config,
      method: "DELETE",
      url,
    });
  }

  /**
   * 取消指定请求
   */
  public cancelRequest(requestId: string): void {
    const cancelToken = this.cancelTokenMap.get(requestId);
    if (cancelToken) {
      cancelToken.cancel(`Request ${requestId} canceled by user`);
      this.cleanup(requestId);
    }
  }

  /**
   * 取消所有请求
   */
  public cancelAllRequests(): void {
    this.cancelTokenMap.forEach((cancelToken, requestId) => {
      cancelToken.cancel(`Request ${requestId} canceled by user`);
    });
    this.cancelTokenMap.clear();
    this.requestQueue.clear();
  }

  /**
   * 获取当前请求数量
   */
  public getActiveRequestCount(): number {
    return this.requestQueue.size;
  }

  /**
   * 检查是否有活跃请求
   */
  public hasActiveRequests(): boolean {
    return this.requestQueue.size > 0;
  }

  /**
   * 更新基础配置
   */
  public updateBaseConfig(name: string, config: Partial<RequestConfig>): void {
    const instance = this.getAxiosInstance(name);
    if (instance) {
      Object.assign(instance.defaults, config);
    }
  }

  /**
   * 获取axios实例
   */
  public getAxiosInstance(name = "default"): AxiosInstance {
    const instance = this.axiosInstances.get(name);

    if (!instance) {
      return this.createInstance(name);
    }

    return instance;
  }

  /**
   * 创建取消令牌
   */
  private createCancelToken(requestId: string): CancelTokenSource {
    const cancelToken = axios.CancelToken.source();
    this.cancelTokenMap.set(requestId, cancelToken);
    return cancelToken;
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 清理请求相关资源
   */
  private cleanup(requestId: string): void {
    this.cancelTokenMap.delete(requestId);
    this.requestQueue.delete(requestId);
  }
}

// 导出类型
export * from "./config";
export * from "./types";

// 默认导出
export default HttpRequestManager;
