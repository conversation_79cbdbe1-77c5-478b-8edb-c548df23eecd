import border from "@assets/common/border.png";
import { AnimatePresence, motion } from "motion/react";
import type React from "react";
import Close from "./Close";
import LoadingSpinner from "./LoadingSpinner";
import QRCode from "./QRCode";

interface QRCodeMaskProps {
  /**
   * 是否显示
   */
  visible: boolean;
  /**
   * 二维码加载中
   */
  loading?: boolean;
  /**
   * 二维码
   */
  value: string;
  /**
   * 关闭
   */
  onClose: () => void;
  /**
   * 标题
   */
  title?: string;
  /**
   * 描述
   */
  desc?: string;
}

const QRCodeMask: React.FC<QRCodeMaskProps> = ({
  visible,
  loading,
  value,
  onClose,
  title,
  desc,
}) => {
  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          className="fixed z-10 top-0 left-0 w-full h-full flex justify-center items-center"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.5 }}
          transition={{ duration: 0.3 }}
        >
          <div className="relative w-[25.52vw] pt-[3.8vw] pb-[7.31vw] bg-mask/90 rounded-xl flex flex-col items-center">
            <Close
              onClick={onClose}
              className="absolute top-[.73vw] right-[.73vw]"
            />

            {/* 文字描述 */}
            <div className="flex flex-col items-center gap-[.83vw]">
              <h1 className="font-ZQKLKT-Bold  font-bold text-gradient text-[2.4vw]">
                {title}
              </h1>
              <p className="text-[1.15vw] text-primary-70">{desc}</p>
            </div>

            <div className="relative w-[17.97vw] h-[16.82vw] mt-[3.96vw]">
              <img src={border} alt="border" className="w-full h-full" />
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[15.31vw] h-[15.31vw]">
                {loading ? (
                  <LoadingSpinner className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2" />
                ) : (
                  <QRCode value={value} />
                )}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default QRCodeMask;
