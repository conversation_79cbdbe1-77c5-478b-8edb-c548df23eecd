import FilePreview from "@/renderer/components/FilePreview";
import FileDrag from "@/renderer/components/StepTwo/FileDrag";
import StepFileTip from "@/renderer/components/StepTwo/StepFileBar";
import StepTools from "@/renderer/components/StepTwo/StepTools";
import { useDragSort } from "@/renderer/hooks";
import useFileAnalysis from "@/renderer/hooks/useFileAnalysis";
import usePolling from "@/renderer/hooks/usePolling";
import { getUploadedFiles } from "@/renderer/service/dispute";
import { useAppDispatch, useAppSelector } from "@/renderer/store/hooks";
import { setFiles } from "@/renderer/store/slice/file";
import { getFileType } from "@/renderer/utils/file";
import { nanoid } from "nanoid";
import React, { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import BigQRCode from "./components/BigQRCode";
import SmallQRCode from "./components/SmallQRCode";

const qrcodeUrl = import.meta.env.VITE_QRCODE_URL;

const StepTwo: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  /** 设备ID */
  const mid = useAppSelector(state => state.client?.mid);
  /** 扫码ID */
  const [tid, setTid] = useState(nanoid(6));

  /** 二维码内容 */
  const QRCodeValue = useMemo(() => {
    return `${qrcodeUrl}?mid=${mid}&tid=${tid}`;
  }, [mid, tid]);

  const [scan, setScan] = useState(true);
  const { files } = useDragSort();

  const { data, isPolling } = usePolling(
    () => {
      if (!mid) return Promise.reject(new Error("设备ID不能为空"));

      return getUploadedFiles({
        mid,
        tid,
      });
    },
    {
      immediate: true,
      interval: 2000,
      maxAttempts: 100,
      shouldContinue: data => {
        return data.data.length > 0 ? false : true;
      },
    }
  );

  useEffect(() => {
    if (data && !isPolling) {
      setScan(false);

      dispatch(
        setFiles(
          data.data.map(item => ({
            id: item.id.toString(),
            name: item.url.split("/").pop() || "",
            type: getFileType(item.url),
            path: item.url,
            selected: false,
            remote: true,
          }))
        )
      );
    }
  }, [data, dispatch, isPolling]);

  /** 预览文件 */
  const [previewIndex, setPreviewIndex] = useState<number>(-1);
  const handleClosePreview = () => {
    setPreviewIndex(-1);
  };

  /** 上一步 */
  const handlePrev = () => {
    navigate(-1);
    // 清空文件
    dispatch(setFiles([]));
  };

  const { fileAnalysis } = useFileAnalysis();
  const client = useAppSelector(state => state.client);

  /** 下一步 */
  const handleNext = () => {
    if (!mid || !client?.disputePageType || !client?.disputeType) return;
    files.forEach(file => fileAnalysis(file, true));
    navigate("/dispute/step-three");
  };

  return (
    <>
      <FilePreview
        files={files}
        index={previewIndex}
        open={previewIndex !== -1}
        onClose={handleClosePreview}
        pdfClassName="h-[45.78vw]"
      />

      <div className="relative w-full h-full flex flex-col overflow-hidden">
        <StepFileTip />

        {files.length > 0 && (
          <div className="flex-1 overflow-hidden p-[2vw]">
            <FileDrag onPreview={(_, index) => setPreviewIndex(index)} />
          </div>
        )}

        <BigQRCode value={QRCodeValue} open={scan} />
        {/* 二维码平台 */}
        <SmallQRCode
          value={QRCodeValue}
          open={!scan}
          onClose={() => {
            setScan(true);
            dispatch(setFiles([]));
            setTid(nanoid(6));
          }}
        />

        {/* 上一步 */}
        <StepTools
          onPrev={handlePrev}
          onNext={handleNext}
          count={files.length}
        />
      </div>
    </>
  );
};

export default StepTwo;
