import allFile from "@assets/step-two/all-file.png";
import SwapOutlined from "@assets/svg/swapOutlined.svg?react";
import { AnimatePresence, motion } from "motion/react";
import { twMerge } from "tailwind-merge";

interface Props {
  type?: "camera" | "usb";
  /** 继续上传文件 */
  onClick?: (type: "camera" | "usb") => void;
  className?: string;
}

const StepFileBar: React.FC<Props> = ({ type, onClick, className }) => {
  return (
    <div className={twMerge("flex justify-between", className)}>
      {/* 全部文件描述 */}
      <div className="h-[2.55vw] relative">
        <img src={allFile} alt="" className="h-full object-contain" />
        <div className="absolute top-[0.83vw] left-[8.33vw] text-[.8vw] leading-none text-primary-70 whitespace-nowrap">
          双击文件可预览/通过点击拖拽可调整文件顺序（分析结果基于您的文件顺序请仔细排序）
        </div>
      </div>

      <AnimatePresence mode="wait" initial={false}>
        {type && (
          <motion.div
            key={type}
            className="px-[0.89vw] py-[0.47vw] h-max leading-none text-[1vw] flex items-center gap-[0.52vw] text-primary-60 border border-primary-60 rounded-full cursor-pointer"
            onClick={() => onClick?.(type === "camera" ? "usb" : "camera")}
            variants={{
              initial: {
                rotateY: -90,
                transformPerspective: 1000,
                transformOrigin: "center",
              },
              animate: {
                rotateY: 0,
                transformPerspective: 1000,
                transformOrigin: "center",
                transition: {
                  duration: 0.3,
                },
              },
              exit: {
                rotateY: 90,
                transformPerspective: 1000,
                transformOrigin: "center",
                transition: {
                  duration: 0.3,
                },
              },
            }}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <SwapOutlined className="w-[1.45vw] h-[1.45vw]" />{" "}
            <span className="min-w-[4.9vw]">
              {type === "camera" ? "拍照上传" : "U盘上传"}
            </span>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default StepFileBar;
