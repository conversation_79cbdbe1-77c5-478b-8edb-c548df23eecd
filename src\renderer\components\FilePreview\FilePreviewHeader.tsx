import previewClose from "@assets/step-two/preview-close.png";
import { motion } from "motion/react";

interface Props {
  index: number;
  name: string;
  count: number;
  onClose: () => void;
}

const FilePreviewHeader: React.FC<Props> = ({
  index,
  name,
  count,
  onClose,
}) => {
  return (
    <div
      className="z-20 w-full h-[4.17vw] bg-[#101e37] flex items-center justify-between px-[1.04vw]"
      style={{
        boxShadow: "0px 0px 30px 20px rgba(0, 0, 0, 0.9)",
      }}
    >
      <div>
        <span className="text-white text-[1.82vw]">{index + 1}</span>
        <span className="text-[#b3b3b3] text-[1.35vw]">/{count}</span>
      </div>
      {/* 名称 */}
      <div className="text-white text-[1.67vw]">{name}</div>

      {/* 关闭按钮 */}
      <motion.img
        src={previewClose}
        alt="preview-close"
        className="w-[1.82vw] h-[1.82vw] cursor-pointer"
        whileTap={{ scale: 0.9 }}
        onClick={onClose}
      />
    </div>
  );
};

export default FilePreviewHeader;
