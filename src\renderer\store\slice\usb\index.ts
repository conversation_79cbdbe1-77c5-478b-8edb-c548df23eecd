import type { UsbInfo } from "@/types/services/usbService";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import usbExtraReducers from "./thunks";
import type { UsbState } from "./types";

// 初始状态
const initialState: UsbState = {
  files: [],
  drives: [],
  loading: false,
  error: null,
  currentPath: null,
  isAttached: false,
  selectedCount: 0,
  uploading: false,
  uploadResult: null,
};

const usbSlice = createSlice({
  name: "usb",
  initialState,
  reducers: {
    // 清除错误
    clearError: state => {
      state.error = null;
    },
    // 设置当前路径
    setCurrentPath: (state, action: PayloadAction<string>) => {
      state.currentPath = action.payload;
      state.isAttached = true;
      // 清除错误状态
      state.error = null;
    },
    // 插入U盘,链接的驱动器列表
    attachUsb: (state, action: PayloadAction<UsbInfo[]>) => {
      state.isAttached = true;
      state.drives = action.payload;
    },
    // 断开U盘连接
    detachUsb: state => {
      state.isAttached = false;
      state.currentPath = null;
      state.files = [];
      state.selectedCount = 0;
    },
    // 清除上传结果
    clearUploadResult: state => {
      state.uploadResult = null;
    },
  },
  extraReducers: usbExtraReducers,
});

export const {
  clearError,
  setCurrentPath,
  attachUsb,
  detachUsb,
  clearUploadResult,
} = usbSlice.actions;

export default usbSlice.reducer;
