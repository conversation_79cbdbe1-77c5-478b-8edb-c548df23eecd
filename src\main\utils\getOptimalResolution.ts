import { screen } from "electron";

// 支持的分辨率
const RESOLUTIONS = {
  HD: { width: 1366, height: 768 },
  FULL_HD: { width: 1920, height: 1080 },
};

// 获取最佳分辨率
const getOptimalResolution = () => {
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // 确保窗口尺寸不小于最小限制
  const minWidth = RESOLUTIONS.HD.width;
  const minHeight = RESOLUTIONS.HD.height;

  // 如果屏幕尺寸小于最小限制，则使用最小限制
  if (width < minWidth || height < minHeight) {
    return RESOLUTIONS.HD;
  }

  // 计算屏幕宽高比
  const screenRatio = width / height;
  // 16:9 标准比例
  const standardRatio = 16 / 9;

  // 判断是否为16:9比例
  const is16x9 = Math.abs(screenRatio - standardRatio) < 0.01;

  // 根据屏幕宽度选择合适的分辨率
  if (is16x9 && width >= RESOLUTIONS.FULL_HD.width) {
    return RESOLUTIONS.FULL_HD;
  } else {
    return RESOLUTIONS.HD;
  }
};

export default getOptimalResolution;

export { RESOLUTIONS };
