import LibreOfficeInstallGuide from "@/renderer/components/LibreOffice/LibreOfficeInstallGuide";
import LibreOfficeUtils from "@/renderer/utils/libreOffice";
import { AlertTriangleIcon } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";
import PDFViewer from "../PDFViewer";
import Loading from "./components/Loading";
import WordTabBar from "./components/WordTabBar";

interface Props {
  /** 文件路径 */
  filePath: string;
  /** 文件名称 */
  fileName: string;
  /** 是否可见 */
  visible: boolean;
  /** 是否显示 TabBar */
  showTabBar?: boolean;
  /** 加载状态变化回调 */
  onLoadingChange?: (loading: boolean) => void;
  /** 自定义样式 */
  className?: string;
  /** 骨架屏自定义样式 */
  skeletonClassName?: string;
}

/**
 * Word 文档预览组件
 * 使用 LibreOffice 将 Word 文档转换为 PDF 进行预览
 */
const WordPreview: React.FC<Props> = ({
  filePath,
  fileName,
  visible,
  showTabBar = false,
  onLoadingChange,
  className,
  skeletonClassName,
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [conversionTime, setConversionTime] = useState<number | null>(null);
  const [showInstallGuide, setShowInstallGuide] = useState(false);
  const [libreOfficeAvailable, setLibreOfficeAvailable] = useState<
    boolean | null
  >(null);

  // 取消标志
  const abortControllerRef = useRef<AbortController | null>(null);

  // 同步加载状态到父组件
  useEffect(() => {
    onLoadingChange?.(loading);
  }, [loading, onLoadingChange]);

  // 取消当前转换任务
  const cancelConversion = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  /**
   * 检查 LibreOffice 可用性并转换文档
   */
  const checkLibreOfficeAndConvert = useCallback(async () => {
    if (!filePath) return;

    setLoading(true);
    setError(null);

    // 取消之前的任务
    cancelConversion();

    // 创建新的AbortController
    abortControllerRef.current = new AbortController();

    try {
      console.log("检查 LibreOffice 可用性...");

      // 检查 LibreOffice 是否可用
      const libreOfficeUtils = LibreOfficeUtils.getInstance();
      const isAvailable = await libreOfficeUtils.checkAvailability();

      // 检查是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        console.log("LibreOffice检查被取消");
        return;
      }

      setLibreOfficeAvailable(isAvailable);

      if (!isAvailable) {
        console.log("LibreOffice 不可用，显示安装指南");
        setShowInstallGuide(true);
        setLoading(false);
        return;
      }

      console.log("LibreOffice 可用，开始转换文档:", fileName);

      // 转换文档
      const result = await libreOfficeUtils.convertToPdfWithCache(filePath);

      // 检查是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        console.log("文档转换被取消");
        return;
      }

      if (result.success && result.outputPath) {
        console.log("文档转换成功:", result.outputPath);
        console.log("转换耗时:", result.duration, "ms");

        // 创建预览 URL
        const previewUrl = new URL(`usb://${result.outputPath}`).href;

        setPreviewUrl(previewUrl);
        setConversionTime(result.duration || null);
      } else {
        throw new Error(result.error || "文档转换失败");
      }
    } catch (err) {
      // 如果是取消操作，不显示错误
      if (abortControllerRef.current?.signal.aborted) {
        console.log("文档转换被取消");
        return;
      }

      console.error("文档转换失败:", err);
      const errorMessage = err instanceof Error ? err.message : "转换失败";
      setError(errorMessage);

      // 如果是 LibreOffice 不可用的错误，显示安装指南
      if (
        errorMessage.includes("LibreOffice") ||
        errorMessage.includes("未安装")
      ) {
        setShowInstallGuide(true);
      }
    } finally {
      setLoading(false);
    }
  }, [filePath, fileName, cancelConversion]);

  useEffect(() => {
    if (visible && filePath) {
      checkLibreOfficeAndConvert();
    }
  }, [visible, filePath, checkLibreOfficeAndConvert]);

  /**
   * 重新转换
   */
  const handleRetry = () => {
    setPreviewUrl(null);
    setError(null);
    setConversionTime(null);
    setShowInstallGuide(false);
    checkLibreOfficeAndConvert();
  };

  /**
   * 关闭安装指南
   */
  const handleCloseInstallGuide = () => {
    setShowInstallGuide(false);
  };

  // 清理函数
  useEffect(() => {
    return () => {
      cancelConversion();
    };
  }, [cancelConversion]);

  if (!visible) return null;

  return (
    <>
      <div
        className={twMerge(
          "w-full h-full flex flex-col bg-white rounded-lg overflow-hidden",
          className
        )}
      >
        {/* 预览头部 */}
        {showTabBar && (
          <WordTabBar
            fileName={fileName}
            conversionTime={conversionTime ?? 0}
            libreOfficeAvailable={!!libreOfficeAvailable}
            error={!!error}
            handleRetry={handleRetry}
            onInstallLibreOffice={() => setShowInstallGuide(true)}
          />
        )}

        {/* 预览内容 */}
        <div className="flex-1 relative overflow-y-auto">
          <Loading visible={loading} />

          {error && !showInstallGuide && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
              <div className="text-center max-w-md">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertTriangleIcon className="w-8 h-8 text-red-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  转换失败
                </h3>
                <p className="text-sm text-gray-600 mb-4">{error}</p>
                <div className="flex space-x-2 justify-center">
                  <button
                    onClick={handleRetry}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                  >
                    重新转换
                  </button>
                  {error.includes("LibreOffice") && (
                    <button
                      onClick={() => setShowInstallGuide(true)}
                      className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
                    >
                      安装指南
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {previewUrl && !loading && !error && (
            <PDFViewer
              filePath={previewUrl}
              fileName={fileName}
              mode="all"
              className="h-max"
              skeletonClassName={skeletonClassName}
            />
          )}

          {/* LibreOffice 不可用时的提示 */}
          {libreOfficeAvailable === false &&
            !loading &&
            !error &&
            !showInstallGuide && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                <div className="text-center max-w-md">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg
                      className="w-8 h-8 text-orange-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    需要安装 LibreOffice
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">
                    要预览 Word 文档，需要先安装 LibreOffice。
                  </p>
                  <button
                    onClick={() => setShowInstallGuide(true)}
                    className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
                  >
                    查看安装指南
                  </button>
                </div>
              </div>
            )}
        </div>
      </div>

      {/* LibreOffice 安装指南 */}
      <LibreOfficeInstallGuide
        visible={showInstallGuide}
        onClose={handleCloseInstallGuide}
      />
    </>
  );
};

export default WordPreview;
