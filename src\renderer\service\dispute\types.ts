import type { DisputePageType, DisputeType } from "@/types/views/dispute";
/**
 * =========================================
 * 要素表名称等数据
 * =========================================
 */
export type DisputeFormPayload = {
  mid: number;
  page?: number;
  limit?: number;
};

export type Passage =
  | "dsrxx" // dsrxx (当事人信息)
  | "ssqqhyj" // ssqqhyj (诉讼请求和依据、约定管辖和诉讼保全)
  | "sshly" // sshly (事实和依据)
  | "dsrxx_1" // dsrxx_1 (当事人信息)
  | "ssqqhyj_1" // ssqqhyj_1 (诉讼请求和依据、约定管辖和诉讼保全)
  | "sshly_1"; // sshly_1 (事实和依据)

export type DisputeForm = {
  id: number;
  label: string;
  intent_type: DisputeType;
  mark: string | null;
  sort_num: number;
  print_count: number;
  use_count: number;
};

export type Formv2Item = DisputeForm & { total_use_cnt: number };

export type PackageItem = {
  /** 功能项id */
  id: number;
  version: number;
  /** 功能项名称 */
  label: string;
  /** 功能项参考号 */
  ref_num: string;
  /** 功能项内容 */
  content: null;
  /** 功能项排序 */
  sort_num: null;
  cid: null;
  mark: null;
  created_at: null;
  updated_at: null;
};

export type DisputeFormv2 = {
  anyou: Formv2Item[];
  items: PackageItem[];
  current_page: number;
  total: number;
};

/**
 * =========================================
 * 案由详情
 * =========================================
 */
export type DisputeFormv2Detail = {
  id: number; //详情数据id
  anyou_id: number;
  data_json: string; //数据
  type_id: number; //分类id
  type_label: string; //分类名
  type_alias: DisputePageType; //分类别名
  file: { id: number; path: string }[]; //文件
};

/**
 * =========================================
 * 提取要素表
 * =========================================
 */
export interface ExtractPayload {
  /** 用户的起诉状内容 */
  query_text: string;

  /** 应用id */
  appid: string;

  /** unix 时间戳（单位：秒），和服务器时间相差5分钟内 */
  timestamp: number;

  /** 签名（不区分大小写），计算方式：(timestamp + appsecret) 的 MD5 值 */
  signature: string;

  /**
   * 提取的段落，可取值：
   * - dsrxx (当事人信息)
   * - ssqqhyj (诉讼请求和依据、约定管辖和诉讼保全)
   * - sshly (事实和依据)
   * - dsrxx_1 (当事人信息)
   * - ssqqhyj_1 (诉讼请求和依据、约定管辖和诉讼保全)
   * - sshly_1 (事实和依据)
   */
  passage: Passage;

  /** 设备id，默认为 "1" */
  mid: number;

  type: DisputeType;

  /**
   * 文书类型
   * - qsz 起诉状（默认）
   * - dbz 答辩状
   */
  doc_type?: DisputePageType;

  /** 是否流式返回，默认为 true */
  stream?: boolean;

  /** 版本号，不同版本号返回格式有差异，默认为 1 */
  version?: number;

  /** 提取要素表的请求id（同次同query_text此参数相同），用于统计使用次数，区分返回值等 */
  show_id?: string;
}

export type ExtractResponse = {
  choices: [
    {
      message: {
        role: "assistant";
        content: string;
        content_ori: string;
      };
      finish_reason: "stop";
      index: number;
      logprobs: null;
    },
  ];
  object: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    prompt_tokens_details: {
      cached_tokens: number;
    };
  };
  created: number;
  system_fingerprint: null;
  model: string;
  id: string;
  is_sudms: boolean;
  session_id: string;
  show_id: string;
};

/**
 * =========================================
 * 轮询获取移送端上传文件
 * =========================================
 */

/** 获取移送端上传文件 */
export type UploadedFilesPayload = {
  /** 设备ID */
  mid: number;
  /** 扫码ID */
  tid: string;
};

export type UploadedFiles = {
  /** 文件ID */
  id: number;
  /** 文件路径 */
  url: string;
  /** 设备ID */
  mid: number;
  /** 扫码ID */
  tid: string;
  /** 创建时间 */
  created_at: string | null;
  /** 标记 */
  mark: string | null;
  /** 分类ID */
  cid: string;
};

/**
 * =========================================
 * 提交使用次数 和打印次数
 * =========================================
 */

export type CountType =
  | {
      /** 类型 */
      type: "print";
      /** 打印的用户，身份证、密码 */
      user: string;
    }
  | {
      /** 类型 */
      type: "use";
      /** 打印的用户，身份证、密码 */
      user?: string;
    };

export type SubmitUseCountPayload = {
  /** 设备ID */
  mid: number;
  /** 纠纷类型 */
  intent_type: DisputeType;
  /** 打印页数 */
  page_count?: number;
} & CountType;
