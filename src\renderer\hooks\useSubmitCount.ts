import { useCallback } from "react";
import { submitUseCount } from "../service/dispute";
import type { CountType } from "../service/dispute/types";
import { useAppSelector } from "../store/hooks";

/**
 * 提交使用次数
 * @returns 返回提交使用次数
 */
const useSubmitCount = () => {
  const mid = useAppSelector(state => state.client?.mid);
  const disputeType = useAppSelector(state => state.client?.disputeType);

  const submitCount = useCallback(
    (count: CountType) => {
      if (mid && disputeType) {
        submitUseCount({
          mid,
          intent_type: disputeType,
          page_count: 1,
          ...count,
        });
      }
    },
    [mid, disputeType]
  );

  return {
    submitCount,
  };
};

export default useSubmitCount;
