# Dependencies
node_modules/
pnpm-lock.yaml
package-lock.json
yarn.lock

# Build outputs
dist/
build/
.vite/
out/

# Electron build directories
release/
packages/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
Thumbs.db
.directory

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Config files that don't need linting
*.config.js
*.config.ts
forge.config.ts
vite.*.config.ts
postcss.config.js
tailwind.config.js

# Generated files
*.d.ts
!src/**/*.d.ts

# Test files (if added later)
**/__tests__/**
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx

# Temporary files
*.tmp
*.temp
