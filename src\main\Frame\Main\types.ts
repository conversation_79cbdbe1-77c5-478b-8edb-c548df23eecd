import { BrowserWindow, BrowserWindowConstructorOptions } from "electron";

/**
 * 窗口配置接口
 */
export type WindowConfig = BrowserWindowConstructorOptions;

/**
 * 窗口状态接口
 */
export interface WindowState {
  /** 是否准备好 */
  isReady: boolean;
  /** 是否可见 */
  isVisible: boolean;
  /** 是否销毁 */
  isDestroyed: boolean;
  /** 窗口宽度 */
  width: number;
  height: number;
}

/**
 * 窗口管理器接口
 */
export interface IWindowManager {
  /** 创建窗口 */
  create(): BrowserWindow;
  /** 获取窗口 */
  getWindow(): BrowserWindow | null;
  /** 是否准备好 */
  isReady(): boolean;
  /** 显示窗口 */
  show(): void;
  /** 隐藏窗口 */
  hide(): void;
  /** 关闭窗口 */
  close(): void;
  /** 设置窗口大小 */
  setSize(width: number, height: number): void;
  /** 获取窗口大小 */
  getSize(): { width: number; height: number };
  /** 是否销毁 */
  readonly destroyed: boolean;
}

export {};
