import { ipcMain, type IpcMainEvent, type IpcMainInvokeEvent } from "electron";

export type IpcHandlerFunction<T = any, R = any> = (
  event: IpcMainInvokeEvent,
  ...args: T[]
) => Promise<R> | R;

export type IpcListenerFunction<T = any> = (
  event: IpcMainEvent,
  ...args: T[]
) => void;

/**
 * IPC 管理器
 */
export class IpcManager {
  private static instance: IpcManager;
  private handlers: Map<string, IpcHandlerFunction> = new Map();
  private listeners: Map<string, IpcListenerFunction[]> = new Map();

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): IpcManager {
    if (!IpcManager.instance) {
      IpcManager.instance = new IpcManager();
    }
    return IpcManager.instance;
  }

  /**
   * 注册 IPC 处理器
   * @param channel 通道名称
   * @param handler 处理函数
   */
  public registerHandler(channel: string, handler: IpcHandlerFunction): void {
    // 如果已经注册过，先移除
    this.removeHandler(channel);

    // 注册新的处理器
    ipcMain.handle(channel, handler);
    this.handlers.set(channel, handler);
  }

  /**
   * 注册 IPC 监听器
   * @param channel 通道名称
   * @param listener 监听函数
   */
  public registerListener(
    channel: string,
    listener: IpcListenerFunction
  ): void {
    // 获取现有的监听器列表
    const existingListeners = this.listeners.get(channel) || [];

    // 添加新的监听器
    existingListeners.push(listener);
    this.listeners.set(channel, existingListeners);

    // 注册到 Electron
    ipcMain.on(channel, listener);
  }

  /**
   * 移除 IPC 处理器
   * @param channel 通道名称
   */
  public removeHandler(channel: string): void {
    if (this.handlers.has(channel)) {
      ipcMain.removeHandler(channel);
      this.handlers.delete(channel);
    }
  }

  /**
   * 移除 IPC 监听器
   * @param channel 通道名称
   * @param listener 监听函数
   */
  public removeListener(channel: string, listener: IpcListenerFunction): void {
    const listeners = this.listeners.get(channel);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
        ipcMain.removeListener(channel, listener);
      }
    }
  }

  /**
   * 移除指定通道的所有监听器
   * @param channel 通道名称
   */
  public removeAllListeners(channel: string): void {
    ipcMain.removeAllListeners(channel);
    this.listeners.delete(channel);
  }

  /**
   * 获取所有已注册的处理器
   */
  public getHandlers(): Map<string, IpcHandlerFunction> {
    return new Map(this.handlers);
  }

  /**
   * 获取所有已注册的监听器
   */
  public getListeners(): Map<string, IpcListenerFunction[]> {
    return new Map(this.listeners);
  }

  /**
   * 获取已注册的通道列表
   */
  public getRegisteredChannels(): string[] {
    const channels = new Set<string>();

    // 添加处理器通道
    this.handlers.forEach((_, channel) => channels.add(channel));

    // 添加监听器通道
    this.listeners.forEach((_, channel) => channels.add(channel));

    return Array.from(channels);
  }

  /**
   * 清理所有 IPC 处理器和监听器
   */
  public cleanup(): void {
    // 清理所有处理器
    this.handlers.forEach((_, channel) => {
      this.removeHandler(channel);
    });

    // 清理所有监听器
    this.listeners.forEach((_, channel) => {
      this.removeAllListeners(channel);
    });
  }

  /**
   * 检查通道是否已注册
   * @param channel 通道名称
   */
  public isChannelRegistered(channel: string): boolean {
    return this.handlers.has(channel) || this.listeners.has(channel);
  }

  /**
   * 获取通道信息
   * @param channel 通道名称
   */
  public getChannelInfo(channel: string): {
    hasHandler: boolean;
    hasListeners: boolean;
    listenerCount: number;
  } {
    return {
      hasHandler: this.handlers.has(channel),
      hasListeners: this.listeners.has(channel),
      listenerCount: this.listeners.get(channel)?.length || 0,
    };
  }
}

export const ipcManager = IpcManager.getInstance();
