import type { LibreOfficeResult } from "@/preload/module/libreOffice/types";

/**
 * LibreOffice 转换缓存项
 */
interface ConversionCacheItem {
  /** 转换结果 */
  result: LibreOfficeResult;
  /** 缓存时间戳 */
  timestamp: number;
  /** 文件修改时间 */
  fileModifiedTime: number;
}

/**
 * LibreOffice 工具类
 * 提供文档转换的缓存和优化功能
 */
class LibreOfficeUtils {
  private static instance: LibreOfficeUtils;
  private cache = new Map<string, ConversionCacheItem>();
  private readonly CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5分钟缓存
  private readonly MAX_CACHE_SIZE = 50; // 最大缓存数量

  // 开发环境模拟模式
  private devMode = {
    enabled: false,
    simulateUnavailable: false,
    simulateError: false,
  };

  private constructor() {
    // 在开发环境中，可以通过 URL 参数控制模拟模式
    if (process.env.NODE_ENV === "development") {
      const urlParams = new URLSearchParams(window.location.search);
      this.devMode.enabled = urlParams.get("libreoffice-dev") === "true";
      this.devMode.simulateUnavailable =
        urlParams.get("simulate-unavailable") === "true";
      this.devMode.simulateError = urlParams.get("simulate-error") === "true";

      if (this.devMode.enabled) {
        console.log("LibreOffice 开发模式已启用", this.devMode);
      }
    }
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LibreOfficeUtils {
    if (!LibreOfficeUtils.instance) {
      LibreOfficeUtils.instance = new LibreOfficeUtils();
    }
    return LibreOfficeUtils.instance;
  }

  /**
   * 转换文档为 PDF（带缓存）
   */
  public async convertToPdfWithCache(
    filePath: string
  ): Promise<LibreOfficeResult> {
    const cacheKey = `pdf:${filePath}`;
    const cached = this.getCachedResult(cacheKey, filePath);

    if (cached) {
      console.log("使用缓存的 PDF 转换结果:", filePath);
      return cached;
    }

    console.log("开始转换文档为 PDF:", filePath);
    const result = await window.electronAPI.libreOffice.convertToPdf(filePath);

    if (result.success) {
      this.cacheResult(cacheKey, result, filePath);
    }

    return result;
  }

  /**
   * 生成文档缩略图（带缓存）
   */
  public async generateThumbnailWithCache(
    filePath: string
  ): Promise<LibreOfficeResult> {
    const cacheKey = `thumb:${filePath}`;
    const cached = this.getCachedResult(cacheKey, filePath);

    if (cached) {
      console.log("使用缓存的缩略图:", filePath);
      return cached;
    }

    console.log("开始生成文档缩略图:", filePath);
    const result =
      await window.electronAPI.libreOffice.generateThumbnail(filePath);

    if (result.success) {
      this.cacheResult(cacheKey, result, filePath);
    }

    return result;
  }

  /**
   * 获取缓存的结果
   */
  private getCachedResult(
    cacheKey: string,
    _filePath: string
  ): LibreOfficeResult | null {
    const cached = this.cache.get(cacheKey);
    if (!cached) return null;

    const now = Date.now();

    // 检查缓存是否过期
    if (now - cached.timestamp > this.CACHE_EXPIRY_TIME) {
      this.cache.delete(cacheKey);
      return null;
    }

    // 检查文件是否被修改
    try {
      // 这里需要获取文件的修改时间，但由于在渲染进程中无法直接访问文件系统
      // 我们暂时跳过文件修改时间检查，或者可以通过 IPC 获取
      return cached.result;
    } catch (error) {
      console.warn("检查文件修改时间失败:", error);
      this.cache.delete(cacheKey);
      return null;
    }
  }

  /**
   * 缓存转换结果
   */
  private cacheResult(
    cacheKey: string,
    result: LibreOfficeResult,
    _filePath: string
  ): void {
    // 检查缓存大小
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestCache();
    }

    this.cache.set(cacheKey, {
      result,
      timestamp: Date.now(),
      fileModifiedTime: Date.now(), // 暂时使用当前时间
    });
  }

  /**
   * 清理最旧的缓存
   */
  private evictOldestCache(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * 清理所有缓存
   */
  public clearCache(): void {
    this.cache.clear();
    console.log("已清理 LibreOffice 转换缓存");
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
  } {
    return {
      size: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE,
      hitRate: 0, // 这里可以添加命中率统计
    };
  }

  /**
   * 检查 LibreOffice 是否可用
   */
  public async checkAvailability(): Promise<boolean> {
    try {
      // 开发环境模拟模式
      if (this.devMode.enabled && this.devMode.simulateUnavailable) {
        console.log("开发模式: 模拟 LibreOffice 不可用");
        return false;
      }

      if (this.devMode.enabled && this.devMode.simulateError) {
        console.log("开发模式: 模拟 LibreOffice 检查错误");
        throw new Error("模拟的 LibreOffice 检查错误");
      }

      const result = await window.electronAPI.libreOffice.checkAvailability();
      return result;
    } catch (error) {
      console.error("检查 LibreOffice 可用性失败:", error);
      return false;
    }
  }

  /**
   * 批量预转换文档
   */
  public async preConvertDocuments(filePaths: string[]): Promise<void> {
    console.log("开始批量预转换文档:", filePaths.length, "个文件");

    const promises = filePaths.map(async filePath => {
      try {
        await this.convertToPdfWithCache(filePath);
      } catch (error) {
        console.warn("预转换文档失败:", filePath, error);
      }
    });

    await Promise.allSettled(promises);
    console.log("批量预转换完成");
  }

  /**
   * 获取开发模式状态
   */
  public getDevModeStatus() {
    return this.devMode;
  }

  /**
   * 设置开发模式
   */
  public setDevMode(
    enabled: boolean,
    simulateUnavailable = false,
    simulateError = false
  ) {
    this.devMode = {
      enabled,
      simulateUnavailable,
      simulateError,
    };
    console.log("LibreOffice 开发模式已更新:", this.devMode);
  }
}

export default LibreOfficeUtils;
