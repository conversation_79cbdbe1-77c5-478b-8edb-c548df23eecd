export interface AccessTokenResponse {
  // 要获取的Access Token
  access_token: string;
  // 该参数忽略
  refresh_token: string;
  // Access Token的有效期(秒为单位，有效期30天)
  expires_in: string;
  // 该参数忽略
  scope: string;
  // 该参数忽略
  session_key: string;
  // 该参数忽略
  session_secret: string;
}

/**
 * =========================================
 * 办公文档识别
 * =========================================
 */
export type LanguageType =
  | "auto_detect"
  | "CHN_ENG"
  | "ENG"
  | "JAP"
  | "KOR"
  | "FRE"
  | "SPA"
  | "POR"
  | "GER"
  | "ITA"
  | "RUS"
  | "DAN"
  | "DUT"
  | "MAL"
  | "SWE"
  | "IND"
  | "POL"
  | "ROM"
  | "TUR"
  | "GRE"
  | "HUN"
  | "THA"
  | "VIE"
  | "ARA"
  | "HIN";

export type ResultType = "big" | "small";
export type BoolString = "true" | "false";
export type WordsType = "handwring_only" | "handprint_mix";

type OCRBasic = {
  /** pdf 页码，仅在 pdf_file 有效时使用，默认 1 */
  pdf_file_num?: string;
  /** ofd 页码，仅在 ofd_file 有效时使用，默认 1 */
  ofd_file_num?: string;
  /** 语言类型，默认 CHN_ENG */
  language_type?: LanguageType;
  /** 是否检测图像朝向，默认 false */
  detect_direction?: BoolString;
};

type OCR = {
  /** 返回行 or 行+字识别结果，默认 big */
  result_type?: ResultType;
  /** 是否返回单字符置信度，仅在 result_type=small 时有效 */
  char_probability?: BoolString;
  /** 是否返回每行识别结果置信度，默认 false */
  line_probability?: BoolString;
  /** 是否返回每行的四角坐标，默认 false */
  disp_line_poly?: BoolString;
  /** 文字类型，默认混排 */
  words_type?: WordsType;
  /** 是否分析文档版面，默认 false */
  layout_analysis?: BoolString;
  /** 是否识别并输出表格，默认 false */
  recg_tables?: BoolString;
  /** 是否识别并输出印章，默认 false */
  recog_seal?: BoolString;
  /** 是否识别并输出公式（LaTeX），默认 false */
  recg_formula?: BoolString;
  /** 是否擦除印章后再识别，默认 false */
  erase_seal?: BoolString;
  /** 是否识别下划线，默认 false */
  disp_underline_analysis?: BoolString;
} & OCRBasic;

export type OCRPayload =
  | ({
      /**
       * 四选一，优先级最高,
       * 图像数据，base64编码后进行urlencode，
       * 要求base64编码和urlencode后大小不超过10M，
       * 最短边至少15px，最长边最大8192px
       */
      image: string;
    } & OCR)
  | ({
      /**
       * 四选一，image 优先时失效
       * 图片的url
       * 支持的图片格式：jpg、png、jpeg、bmp、tiff、pdf
       * 支持的图片大小：小于10M
       */
      url?: string;
    } & OCR)
  | ({
      /** 四选一，image/url 优先时失效 */
      pdf_file: string;
    } & OCR)
  | ({
      /** 四选一，image/url 优先时失效 */
      ofd_file: string;
    } & OCR);

export interface OCRResponse {
  /** 唯一日志ID，用于问题排查 */
  log_id: number;

  /** 图像方向（仅 detect_direction=true 时返回）
   *  0：正向，1：逆时针90度，2：逆时针180度，3：逆时针270度 */
  img_direction?: number;

  /** 识别结果数量 */
  results_num: number;

  /** 识别结果数组 */
  results: OCRResult[];

  /** 下划线识别结果，需开启 disp_underline_analysis */
  underline?: Underline[];

  /** 文档版面分析模块数量（需 layout_analysis=true） */
  layouts_num?: number;

  /** 文档版面模块数组（图表、标题、目录等） */
  layouts?: Layout[];

  /** 栏布局的行数（MxN 网格） */
  sec_rows?: number;

  /** 栏布局的列数（MxN 网格） */
  sec_cols?: number;

  /** 文档页眉、页脚、页码等属性信息 */
  sections?: Section[];

  /** 表格总数，需开启 recg_tables */
  table_num?: number;

  /** 表格详细识别结果 */
  tables_result?: TableResult[];

  /** 识别到的印章数量，需开启 recog_seal */
  seal_recog_num?: number;

  /** 印章识别结果 */
  seal_recog_results?: SealResult[];

  /** 识别到的公式内容，需开启 recg_formula */
  formula_result?: FormulaResult[];

  /** PDF 页数（仅 pdf_file 有效时返回） */
  pdf_file_size?: string;

  /** OFD 页数（仅 ofd_file 有效时返回） */
  ofd_file_size?: string;
}

interface OCRResult {
  /** 文字类型：handwriting（手写）| print（印刷） */
  words_type: "handwriting" | "print";

  /** 每行文本识别结果 */
  words: LineResult[];

  /** 单字符识别结果（需 result_type=small） */
  chars?: CharResult[];
}
interface LineResult {
  /** 行置信度（需 line_probability=true） */
  line_probability?: {
    /** 平均置信度 */
    average?: number;
    /** 最低字符置信度 */
    min?: number;
  };

  /** 当前整行识别内容 */
  word: string;

  /** 四角坐标位置（需 disp_line_poly=true） */
  poly_location?: Point[];

  /** 每行文字矩形框位置 */
  words_location: Rectangle[];
}
interface CharResult {
  /** 单个字符内容 */
  char: string;

  /** 单个字符置信度（需 char_probability=true） */
  char_prob?: number;

  /** 单个字符位置信息 */
  chars_location?: Rectangle[];
}
interface Underline {
  /** 下划线坐标起止点 */
  points?: UnderlinePoint[];

  /** 每条下划线的置信度（范围 [0, 1]） */
  prob?: number[];
}

interface UnderlinePoint {
  start_x: number;
  start_y: number;
  end_x: number;
  end_y: number;
}
interface Layout {
  /** 类型：表格/图片/段落/标题等 */
  layout:
    | "table"
    | "figure"
    | "text"
    | "title"
    | "contents"
    | "seal"
    | "table_title"
    | "figure_title"
    | "doc_title";

  /** 当前框体识别概率 */
  layout_prob?: number;

  /** 四角坐标位置 */
  layout_location?: Point[];

  /** 当前 layout 中对应结果在 results 中的索引列表 */
  layout_idx?: number[];
}
interface Section {
  /** 属性类型 */
  attribute: "section" | "header" | "footer" | "number" | "footnote";

  /** 属性框识别置信度 */
  sections_prob?: number;

  /** 四角坐标位置 */
  attri_location?: Point[];

  /** 内容行索引信息 */
  sec_idx?: {
    /** results 行号索引 */
    idx?: string;
    /** layout 内结构顺序号（仅 attribute=section） */
    para_idx?: string;
    /** 所属栏行号 */
    row_idx?: string;
    /** 所属栏列号 */
    col_idx?: string;
  };
}
interface TableResult {
  /** 表格四角坐标 */
  table_location: Point[];

  /** 表头信息 */
  header: TableRow[];

  /** 表格正文单元格信息 */
  body: TableCell[];

  /** 表尾信息 */
  footer: TableRow[];
}

interface TableRow {
  location: Point[];
  /** 多行文字用换行符分隔 */
  words: string;
}

interface TableCell {
  /** 单元格坐标 */
  cell_location: Point[];
  row_start: number[];
  row_end: number[];
  col_start: number[];
  col_end: number[];
  /** 单元格内容 */
  words: string;

  /** 单元格内容的逐行文本（包含位置） */
  contents?: {
    poly_location?: Point[];
    word?: string;
  }[];
}
interface SealResult {
  /** 印章位置 */
  location?: Rectangle;

  /** 印章置信度 */
  probability?: number;

  /** 印章类型：circle / ellipse / rectangle */
  type?: "circle" | "ellipse" | "rectangle";

  /** 印章主字段 */
  major?: {
    words?: string;
    probability?: number;
  };

  /** 印章次字段（多个） */
  minor?: {
    words?: string;
    probability?: number;
  }[];
}
interface FormulaResult {
  /** 公式位置（矩形框） */
  form_location: Rectangle[];

  /** 公式内容（LaTeX 格式） */
  form_words: string;
}
/** 点坐标 */
interface Point {
  x: number;
  y: number;
}

/** 矩形坐标（左上角 + 宽高） */
interface Rectangle {
  left: number;
  top: number;
  width: number;
  height: number;
}

/**
 * =========================================
 * 文档解析-提交请求
 * =========================================
 */
export interface AnalysisPayload {
  /** 文件名，请保证文件名后缀正确，例如 "1.pdf " */
  file_name: string;
  /** 文件的base64编码数据 */
  file_data?: string;
  /** 文件的url */
  file_url?: string;
  return_doc_chunks?: {
    /** 是否进行文档内容切分，「default=False」 */
    switch?: boolean;
    /** 切分块的大小， */
    chunk_size?: number;
    /** 切分类型，chunk 表示按字符切分，mark 表示按标点切分 */
    split_type?: "chunk" | "mark";
    /** 切分标点，仅包含表示一句话结束的标点。 */
    chunk_overlap?: ("。" | "；" | "！" | "？" | ";" | "!" | "?")[];
  };
}

export interface AnalysisResponse {
  /** 唯一请求标识，用于日志定位 */
  log_id: number;

  /** 错误码，0 表示成功，非 0 表示失败 */
  error_code: number;

  /** 错误信息描述 */
  error_msg: string;

  /** 文档解析结果对象 */
  result: ParseTaskResult;
}

export interface ParseTaskResult {
  /** 当前解析任务的唯一ID */
  task_id: string;

  /** 任务状态：
   * - pending：排队中
   * - processing：解析中
   * - success：成功完成
   * - failed：失败 */
  status: "pending" | "processing" | "success" | "failed" | "running";

  /** 错误信息（仅 status=failed 时存在） */
  task_error?: string;

  /** 文档解析后的 Markdown 链接（有效期30天） */
  markdown_url?: string;

  /** 文档解析后的原始结构化结果链接（有效期30天） */
  parse_result_url?: string;
}

/**
 * =========================================
 * 文档解析-获取结果
 * =========================================
 */

export interface TaskAnalysisResponse extends AnalysisResponse {
  result: ParseResult;
}

export interface ParseResult {
  /** 解析任务 ID */
  task_id: string;

  /** 任务状态：
   * - pending：排队中
   * - processing：运行中
   * - success：成功
   * - failed：失败
   */
  status: "pending" | "processing" | "success" | "failed" | "running";

  /** 任务失败时的错误描述（如解析失败、额度不足等） */
  task_error?: string;

  /** Markdown 格式结果链接，有效期 30 天 */
  markdown_url?: string;

  /** BOS 结构化结果链接，有效期 30 天 */
  parse_result_url?: string;
}

/**
 * =========================================
 * 文档解析-获取结果-JSON
 * =========================================
 */

export interface ParseResultJSON {
  /** 文档名称 */
  file_name: string;

  /** 文档ID */
  file_id: string;

  /** 每页解析结果 */
  pages: ParsedPage[];

  /** 文档内容切片（需 return_doc_chunks=true） */
  chunks?: Chunk[];
}

export interface ParsedPage {
  /** 页码 ID */
  page_id: string;

  /** 页码数 */
  page_num: number;

  /** 当前页的纯文字内容 */
  text: string;

  /** 页面中的布局元素（段落、图片、表格等） */
  layouts: LayoutElement[];

  /** 当前页中解析出的表格 */
  tables: TableElement[];

  /** 当前页中解析出的图片 */
  images: ImageElement[];

  /** 页面元信息 */
  meta: PageMeta;
}

export interface LayoutElement {
  /** layout ID，如 "xxxxx-layout-3" */
  layout_id: string;

  /** 文本内容（type 为 table/image 时为空） */
  text: string;

  /** 位置坐标：[x, y, w, h] */
  position: [number, number, number, number];

  /** 类型：
   * - para（段落）
   * - table（表格）
   * - head_tail（页眉页脚）
   * - image（图片）
   * - contents（目录）
   * - seal（印章）
   * - title（标题） */
  type:
    | "para"
    | "table"
    | "head_tail"
    | "image"
    | "contents"
    | "seal"
    | "title";

  /** 子类型（仅当 type 为 title/image 时存在） */
  sub_type?: string;

  /** 标题结构树中的父节点 ID（如为一级标题则为 "root"） */
  parent?: string;

  /** 子节点 layout_id 列表 */
  children?: string[];
}

export interface TableElement {
  /** layout ID，与 layouts 中 type=table 的元素对应 */
  layout_id: string;

  /** 表格内容的 Markdown 格式 */
  markdown: string;

  /** 表格标题对应的 layout_id */
  table_title_id: string[] | null;

  /** 表格位置 [x, y, w, h] */
  position: [number, number, number, number];

  /** 表格中的单元格嵌套布局信息 */
  cells: any[]; // 未提供具体结构，如需可细化

  /** 表格矩阵，表示表格内单元格布局，matrix[i][j] 为 cells 中的索引 */
  matrix: number[][];

  /** 跨页表格标记
   * - "begin"：跨页开始
   * - "inner"：中间页
   * - "end"：结束页
   * - null：非跨页 */
  merge_table: "begin" | "inner" | "end" | null;
}

export interface ImageElement {
  /** layout ID，与 layouts 中 type=image 的元素对应 */
  layout_id: string;

  /** 图片标题 layout_id（无标题则为 null） */
  image_title_id: string[] | null;

  /** 图片位置 [x, y, w, h] */
  position: [number, number, number, number];

  /** 图片内容中的嵌套 layout 信息 */
  content_layouts: any[]; // 可根据上下文再细化
}

export interface PageMeta {
  /** 页面宽度 */
  page_width: number;

  /** 页面高度 */
  page_height: number;

  /** 是否为扫描件 */
  is_scan: boolean;

  /** 页面倾斜角度 */
  page_angle: number;

  /** 页面类型：text / contents / appendix / others */
  page_type: "text" | "contents" | "appendix" | "others";

  /** Excel 的 sheet 名（如适用） */
  sheet_name: string;
}

export interface Chunk {
  /** 切片ID */
  chunk_id: string;

  /** 切片内容（纯文本或表格） */
  content: string;

  /** 类型：text 或 table */
  type: "text" | "table";

  /** 元信息 */
  meta: {
    /** 多级标题内容，例如 ["第1章", "第1节", "标题"] */
    title: string[];

    /** chunk 跨页坐标位置（如适用） */
    position: any[];

    /** chunk 的坐标框位置（左上角 + 宽高） */
    box: any[];

    /** 所属页码 */
    page_num: number;
  };
}

/**
 * =========================================
 * 通用文字识别-高精度版
 * =========================================
 */

export interface OCRBasicExtra extends OCRBasic {
  /**
   * 是否输出段落信息（默认 false）
   */
  paragraph?: "true" | "false";

  /**
   * 是否返回识别结果中每一行的置信度（默认 false）
   */
  probability?: "true" | "false";
}

export type OCRAccurateBasicPayload =
  | ({
      /**
       * 四选一，优先级最高,
       * 图像数据，base64编码后进行urlencode，
       * 要求base64编码和urlencode后大小不超过10M，
       * 最短边至少15px，最长边最大8192px
       */
      image: string;
    } & OCRBasicExtra)
  | ({
      /**
       * 四选一，image 优先时失效
       * 图片的url
       * 支持的图片格式：jpg、png、jpeg、bmp、tiff、pdf
       * 支持的图片大小：小于10M
       */
      url?: string;
    } & OCRBasicExtra)
  | ({
      /** 四选一，image/url 优先时失效 */
      pdf_file: string;
    } & OCRBasicExtra)
  | ({
      /** 四选一，image/url 优先时失效 */
      ofd_file: string;
    } & OCRBasicExtra);

export interface OCRAccurateBasicResponse {
  /**
   * 唯一的 log id，用于问题定位
   */
  log_id: number; // uint64

  /**
   * 图像方向，当 detect_direction=true 时返回
   * -1: 未定义，0: 正向，1: 逆时针90度，2: 逆时针180度，3: 逆时针270度
   */
  direction?: number; // int32

  /**
   * 识别结果数，对应 words_result 数组元素个数
   */
  words_result_num: number; // uint32

  /**
   * 识别结果数组
   */
  words_result: Array<{
    /**
     * 识别结果字符串
     */
    words?: string;

    /**
     * 行置信度信息，只有在 probability=true 时返回
     */
    probability?: {
      average: number;
      variance: number;
      min: number;
    };
  }>;

  /**
   * 段落检测结果，只有在 paragraph=true 时返回
   */
  paragraphs_result?: Array<{
    /**
     * 一个段落包含的行序号数组
     */
    words_result_idx?: number[];
  }>;

  /**
   * 段落识别结果数量（paragraphs_result 的元素个数）
   */
  paragraphs_result_num?: number;

  /**
   * PDF 文件的总页数，仅当 pdf_file 参数有效时返回
   */
  pdf_file_size?: string;

  /**
   * OFD 文件的总页数，仅当 ofd_file 参数有效时返回
   */
  ofd_file_size?: string;
}
