import { IPC_CHANNELS } from "@/shared/constants";
import { ipc<PERSON><PERSON><PERSON> } from "electron";
import type { LibreOfficeAPI } from "./types";

const libreOfficeAPI: LibreOfficeAPI = {
  // 转换文档为 PDF
  convertToPdf: (filePath: string) =>
    ipcRenderer.invoke(IPC_CHANNELS.LIBRE_OFFICE.CONVERT_TO_PDF, filePath),

  // 转换文档为图片
  convertToImage: (filePath: string, format: "png" | "jpg" = "png") =>
    ipcRenderer.invoke(
      IPC_CHANNELS.LIBRE_OFFICE.CONVERT_TO_IMAGE,
      filePath,
      format
    ),

  // 生成文档缩略图
  generateThumbnail: (filePath: string) =>
    ipcRenderer.invoke(IPC_CHANNELS.LIBRE_OFFICE.GENERATE_THUMBNAIL, filePath),

  // 检查 LibreOffice 是否可用
  checkAvailability: () =>
    ipcRenderer.invoke(IPC_CHANNELS.LIBRE_OFFICE.CHECK_AVAILABILITY),
};

export default libreOfficeAPI;
