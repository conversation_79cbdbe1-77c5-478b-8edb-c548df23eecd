import { addPublicPrefix } from "@/main/utils/path";
import { getFileCover } from "@/renderer/utils/file";
import type { UsbFileInfo } from "@/types";
import { AnimatePresence, motion } from "motion/react";
import CardIndex from "../CardIndex";

interface Props {
  files: UsbFileInfo[];
  activeIndex: number;
  setActiveIndex: (index: number) => void;
}

const ThumbnailList: React.FC<Props> = ({
  files,
  activeIndex,
  setActiveIndex,
}) => {
  return (
    <AnimatePresence>
      <div className="absolute top-0 right-0 py-[5vw] px-[2.25vw] h-full overflow-auto flex flex-col gap-[1.2vw] scrollbar-hidden">
        {files.map((file, index) => {
          const isActive = index === activeIndex;
          return (
            <div
              key={index}
              onClick={() => setActiveIndex(index)}
              className="relative w-[10vw] flex-shrink-0 h-[8vw] rounded-[10px] bg-white p-[4px] cursor-pointer"
            >
              {isActive && (
                <motion.div
                  layoutId="card-border"
                  className="absolute inset-0 border-[4px] border-[#3a87f1] rounded-[10px] pointer-events-none z-10"
                />
              )}
              <div className="relative w-full h-full rounded-[6px] overflow-hidden">
                <img
                  src={getFileCover(
                    file.type,
                    addPublicPrefix(file.path, {
                      remote: file.remote,
                    })
                  )}
                  alt={file.name}
                  className="w-full h-full object-contain p-[1vw]"
                  draggable={false}
                />
                <CardIndex index={index + 1} />
              </div>
            </div>
          );
        })}

        <div
          className="fixed top-0 right-[calc(15vw-4px)] h-full w-[4px]"
          style={{
            background:
              "linear-gradient(to bottom, #ffffff00, #3c5d8a, #ffffff00)",
          }}
        ></div>
      </div>
    </AnimatePresence>
  );
};

export default ThumbnailList;
