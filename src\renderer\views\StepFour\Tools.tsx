import CheckBox from "@/renderer/components/CheckBox";
import useFilesAction from "@/renderer/hooks/useFilesAction";
import type { SendMessage } from "@/renderer/hooks/useIframeMessage";
import { useAppDispatch } from "@/renderer/store/hooks";
import { restore } from "@/renderer/store/slice/client";
import append from "@assets/step-four/append.png";
import close from "@assets/step-four/close.png";
import localDownload from "@assets/step-four/local-download.png";
import print from "@assets/step-four/print.png";
import scanPdf from "@assets/step-four/scan-pdf.png";
import scanWord from "@assets/step-four/scan-word.png";
import { motion } from "motion/react";
import React from "react";
import { useNavigate } from "react-router-dom";

interface ToolsProps {
  /**
   * 发送消息
   */
  sendMessage: SendMessage;
  /**
   * 是否双面打印
   */
  duplex: boolean;
  setDuplex: (v: boolean) => void;
}

const Tools: React.FC<ToolsProps> = ({ sendMessage, duplex, setDuplex }) => {
  const dispatch = useAppDispatch();
  const { clearFolder } = useFilesAction();
  const navigate = useNavigate();

  /**
   * 打印
   */
  const handlePrint = () => {
    sendMessage({
      type: "GET_DOC_ARRAY_BUFFER",
      payload: "print",
    });
  };

  /**
   * 扫描
   */
  const handlePdfScan = () => {
    sendMessage({
      type: "GET_DOC_ARRAY_BUFFER",
      payload: "scanPdf",
    });
  };

  /**
   * 扫描
   */
  const handleWordScan = () => {
    sendMessage({
      type: "GET_DOC_ARRAY_BUFFER",
      payload: "scanWord",
    });
  };

  /**
   * 本地下载
   */
  const handleLocalDownload = () => {
    sendMessage({
      type: "GET_DOC_ARRAY_BUFFER",
      payload: "download",
    });
  };

  /**
   * 关闭
   */
  const handleClose = () => {
    navigate("/dispute/step-one");
    clearFolder();
    dispatch(restore());
  };

  return (
    <div className="px-[1.4vw] flex flex-col">
      <div className="w-[9.9vw] h-[9.9vw] mb-[2.08vw] cursor-pointer">
        <img src={append} alt="append" className="w-full h-full" />
      </div>

      <CheckBox
        value={duplex}
        onChange={setDuplex}
        className="w-[1.04vw] h-[1.04vw] rounded-sm"
        containerClassName="mb-[.52vw]"
      >
        <span className="text-gradient text-[1.04vw]">双面打印</span>
      </CheckBox>

      <div className="flex flex-col gap-[1.2vw]">
        <motion.img
          src={print}
          alt="print"
          className="w-[9.53vw] h-[3.23vw] cursor-pointer"
          whileTap={{ scale: 0.9 }}
          onClick={handlePrint}
        />

        <motion.img
          src={scanPdf}
          alt="scan-pdf"
          className="w-[9.53vw] h-[3.23vw] cursor-pointer"
          whileTap={{ scale: 0.9 }}
          onClick={handlePdfScan}
        />
        <motion.img
          src={scanWord}
          alt="scan-word"
          className="w-[9.53vw] h-[3.23vw] cursor-pointer"
          whileTap={{ scale: 0.9 }}
          onClick={handleWordScan}
        />

        <motion.img
          src={localDownload}
          alt="local-download"
          className="w-[9.53vw] h-[3.23vw] cursor-pointer"
          whileTap={{ scale: 0.9 }}
          onClick={handleLocalDownload}
        />
      </div>

      <motion.img
        src={close}
        alt="print"
        className="w-[9.53vw] h-[3.23vw] cursor-pointer mt-auto"
        whileTap={{ scale: 0.9 }}
        onClick={handleClose}
      />
    </div>
  );
};

export default Tools;
