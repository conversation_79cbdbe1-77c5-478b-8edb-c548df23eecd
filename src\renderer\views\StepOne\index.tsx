import LoadingSpinner from "@/renderer/components/LoadingSpinner";
import { useDispute } from "@/renderer/hooks";
import { getDisputeFormv2Detail } from "@/renderer/service/dispute";
import type { DisputeForm } from "@/renderer/service/dispute/types";
import { useAppDispatch, useAppSelector } from "@/renderer/store/hooks";
import {
  clearDisputeType,
  setActiveDispute,
  setDisputePageType,
  setDisputeType,
} from "@/renderer/store/slice/client";
import { STATUS_CODE } from "@/renderer/utils/request";
import type { DisputePageType } from "@/types/views/dispute";
import { useEffect, useState } from "react";
import DisputeCard from "./components/DisputeCard";
import DisputeSelectModal from "./components/DisputeSelectModal";
import Pagenation from "./components/Pagenation";

const StepOne = () => {
  const dispatch = useAppDispatch();
  const { forms, loading } = useDispute();
  const client = useAppSelector(state => state.client);
  // 纠纷详情加载
  const [cardLoading, setCardLoading] = useState(false);

  /** 点击纠纷卡片时触发 */
  const handleDisputeCardClick = async (disputeItem: DisputeForm) => {
    dispatch(setDisputeType(disputeItem.intent_type));
    setCardLoading(true);
    const res = await getDisputeFormv2Detail({
      anyou_id: disputeItem.id,
    }).finally(() => {
      setCardLoading(false);
    });

    if (res.code === STATUS_CODE.SUCCESS) {
      dispatch(setActiveDispute(res.data));
    }
  };

  /** 切换纠纷表单类型时触发 */
  const handleDisputePageChange = (type: DisputePageType) => {
    dispatch(setDisputePageType(type));
  };

  /** 关闭弹窗时触发 */
  const handleClose = () => {
    dispatch(clearDisputeType());
  };

  /** 重制客户端状态 */
  useEffect(() => {
    dispatch(clearDisputeType());
  }, [dispatch]);

  return (
    <div className="h-full flex flex-col">
      <LoadingSpinner
        text="正在加载纠纷表单数据..."
        show={loading}
        className="h-full absolute top-0 left-0 right-0 bottom-0  z-10"
      />

      <div className="grid grid-cols-4 gap-[1.354vw] px-[2.5vw] py-[3vw] overflow-auto custom-scrollbar scrollbar-mt-1vw scrollbar-mb-1vw">
        {forms?.map(item => (
          <DisputeCard
            key={item.id}
            useCount={item.use_count}
            printCount={item.print_count}
            disputeType={item.label}
            disputeDescription={item.mark ?? ""}
            onClick={() => handleDisputeCardClick(item)}
          />
        ))}
      </div>

      <div className="flex-1"></div>

      <DisputeSelectModal
        loading={cardLoading}
        open={!!client?.disputeType}
        disputeTypeCode={client?.disputeType}
        onClose={handleClose}
        onDisputePageChange={handleDisputePageChange}
      />

      <Pagenation />
    </div>
  );
};

export default StepOne;
