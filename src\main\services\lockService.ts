import { LOCK_CHANNELS } from "@/shared/constants/lock";
import { exec } from "child_process";
import { app } from "electron";
import { existsSync } from "fs";
import path from "path";
import { ipcManager } from "../ipc/IpcManager";

const exeName = "NativeTask.exe";
function getExePath() {
  return app.isPackaged
    ? path.join(process.resourcesPath, "resources", `${exeName}`) // 生产环境
    : path.join(__dirname, "../../resources", `${exeName}`); // 开发环境
}

/**
 * 检查锁状态
 * @returns 是否有效
 */
export const checkLock = (type: 1 | 2): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    const filePath = getExePath();
    if (!existsSync(filePath)) {
      return reject(new Error("文件不存在"));
    }
    exec(
      filePath + ` cs ${type} Show_Concentrate`,
      (error: Error | null, stdout: string, stderr: string) => {
        if (error) {
          console.error(`执行错误: ${error.message}`);
          reject(error);
          return;
        }
        if (stderr) {
          console.error(`STDERR: ${stderr}`);
        }
        console.log(`STDOUT: ${stdout}`);
        resolve(stdout.trim() === "0");
      }
    );
  });
};

interface LockServiceOptions {
  type: 1 | 2;
}

class LockService {
  private static instance: LockService;

  private constructor(private options: LockServiceOptions) {
    this.setupIpcHandlers();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(options: LockServiceOptions): LockService {
    if (!LockService.instance) {
      LockService.instance = new LockService(options);
      LockService.instance.startTimeoutCheck();
    }
    return LockService.instance;
  }

  /**
   * 设置IPC处理
   */
  private setupIpcHandlers() {
    ipcManager.registerHandler(LOCK_CHANNELS.CHECK, async () =>
      checkLock(this.options.type)
    );
  }

  /**
   * 启动定时检查锁状态
   */
  private startTimeoutCheck() {
    setInterval(async () => {
      const ret = await checkLock(this.options.type);
      if (!ret) {
        console.error("定时检测到无效的锁状态");
        app.exit(0);
        process.exit(0);
      }
    }, 60 * 1000);
  }
}

export default LockService;
