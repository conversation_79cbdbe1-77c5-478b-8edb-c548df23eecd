import react from "@vitejs/plugin-react";
import path from "path";
import { defineConfig } from "vite";
import svgr from "vite-plugin-svgr";

// https://vitejs.dev/config
export default defineConfig(({ mode }) => ({
  plugins: [react(), svgr()],
  publicDir: "./public",
  server: {
    proxy: {
      "/baidu_ocr": {
        target: "https://aip.baidubce.com",
        changeOrigin: true,
        rewrite: path => path.replace(/^\/baidu_ocr/, ""),
      },
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@assets": path.resolve(__dirname, "src/renderer/assets"),
    },
  },
  esbuild:
    mode === "production"
      ? {
          drop: ["console", "debugger"],
        }
      : {},
}));
