import {
  createAsyncThunk,
  type ActionReducerMapBuilder,
} from "@reduxjs/toolkit";
import type { UsbState } from "./types";

// 读取U盘文件
export const readUsbFiles = createAsyncThunk(
  "usb/readFiles",
  async (_, { rejectWithValue }) => {
    try {
      const files = await window.electronAPI.usb.readFiles();
      return files;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "读取U盘文件失败"
      );
    }
  }
);

// 选择U盘路径
export const selectUsbPath = createAsyncThunk(
  "usb/selectPath",
  async (_, { rejectWithValue }) => {
    try {
      const path = await window.electronAPI.usb.selectPath();
      return path;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "选择U盘路径失败"
      );
    }
  }
);

// 设置U盘路径
export const setUsbPath = createAsyncThunk(
  "usb/setPath",
  async (path: string, { rejectWithValue }) => {
    try {
      const success = await window.electronAPI.usb.setPath(path);
      if (success) {
        return path;
      } else {
        throw new Error("设置路径失败");
      }
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "设置U盘路径失败"
      );
    }
  }
);

// 切换文件选择状态
export const toggleFileSelection = createAsyncThunk(
  "usb/toggleFileSelection",
  async (
    { fileId, selected }: { fileId: string; selected: boolean },
    { rejectWithValue }
  ) => {
    try {
      const success = await window.electronAPI.usb.toggleFileSelection(
        fileId,
        selected
      );
      if (success) {
        return { fileId, selected };
      } else {
        throw new Error("切换文件选择状态失败");
      }
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "切换文件选择状态失败"
      );
    }
  }
);

// 获取选中的文件
export const getSelectedFiles = createAsyncThunk(
  "usb/getSelectedFiles",
  async (_, { rejectWithValue }) => {
    try {
      const files = await window.electronAPI.usb.getSelectedFiles();
      return files;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "获取选中文件失败"
      );
    }
  }
);

// 清空选择
export const clearSelection = createAsyncThunk(
  "usb/clearSelection",
  async (_, { rejectWithValue }) => {
    try {
      await window.electronAPI.usb.clearSelection();
      return true;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "清空选择失败"
      );
    }
  }
);

const usbExtraReducers = (builder: ActionReducerMapBuilder<UsbState>) => {
  // 读取U盘文件
  builder
    .addCase(readUsbFiles.pending, state => {
      state.loading = true;
      state.error = null;
    })
    .addCase(readUsbFiles.fulfilled, (state, action) => {
      state.loading = false;
      state.files = action.payload;
      state.selectedCount = action.payload.filter(f => f.selected).length;
    })
    .addCase(readUsbFiles.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

  // 选择U盘路径
  builder
    .addCase(selectUsbPath.pending, state => {
      state.loading = true;
      state.error = null;
    })
    .addCase(selectUsbPath.fulfilled, (state, action) => {
      state.loading = false;
      if (action.payload) {
        state.currentPath = action.payload;
        state.isAttached = true;
      }
    })
    .addCase(selectUsbPath.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

  // 设置U盘路径
  builder
    .addCase(setUsbPath.pending, state => {
      state.loading = true;
      state.error = null;
    })
    .addCase(setUsbPath.fulfilled, (state, action) => {
      state.loading = false;
      state.currentPath = action.payload;
      state.isAttached = true;
    })
    .addCase(setUsbPath.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

  // 切换文件选择状态
  builder.addCase(toggleFileSelection.fulfilled, (state, action) => {
    const { fileId, selected } = action.payload;
    const file = state.files.find(f => f.id === fileId);
    if (file) {
      file.selected = selected;
      state.selectedCount = state.files.filter(f => f.selected).length;
    }
  });

  // 获取选中的文件
  builder.addCase(getSelectedFiles.fulfilled, (state, action) => {
    // 可以用来同步选中状态
    state.selectedCount = action.payload.length;
  });

  // 清空选择
  builder.addCase(clearSelection.fulfilled, state => {
    state.files.forEach(file => {
      file.selected = false;
    });
    state.selectedCount = 0;
  });
};

export default usbExtraReducers;
