import path from "path";
import { defineConfig } from "vite";

export default defineConfig({
  build: {
    emptyOutDir: true,
    rollupOptions: {
      input: {
        main: "src/main/index.ts",
      },
      external: ["drivelist", "usb", "mammoth", "pdf-parse", "pdf-to-printer"],
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  define: {
    "process.env.DONGLE_CHECK": process.env.DONGLE_CHECK || "false",
  },
});
