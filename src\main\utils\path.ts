/**
 * 判断是否为远程URL
 * @param path 路径
 * @returns 返回是否为远程URL
 */
const isRemoteUrl = (path: string) => /^https?:\/\//.test(path);

/**
 * 判断是否为本地路径
 */
const isLocalPath = (path: string) => !isRemoteUrl(path);

/**
 * 添加公共前缀
 * @param path 路径
 * @param options 选项
 * @returns 返回添加公共前缀后的路径
 */
const addPublicPrefix = (
  path: string,
  options?: { remote?: "usb" | "ocx" | boolean; prefix?: string }
) => {
  const { remote = false, prefix } = options || {};

  if (prefix !== undefined) return `${prefix}${path}`;

  switch (remote) {
    case "ocx":
      return `localFile:${path}`;
    case true:
      return `${import.meta.env.VITE_PUBLIC_URL}${path.startsWith("/") ? path : `/${path}`}`;
    case "usb":
    default:
      return `usb:${path}`;
  }
};

export { addPublicPrefix, isLocalPath, isRemoteUrl };
