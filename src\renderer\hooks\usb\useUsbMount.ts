import { useAppDispatch } from "@/renderer/store/hooks";
import { attachUsb, detachUsb } from "@/renderer/store/slice/usb";
import { useEffect } from "react";
import { useUsb } from "./useUsb";

const useUsbMount = () => {
  const dispatch = useAppDispatch();

  const { readFiles } = useUsb();

  useEffect(() => {
    window.electronAPI?.usb
      .onUsbRenderReady()
      .then(data => data.attach && dispatch(attachUsb(data.usbList)));
  }, [dispatch]);

  useEffect(() => {
    window.electronAPI?.usb.onUsbChange(data => {
      if (data.attach) {
        dispatch(attachUsb(data.usbList ?? []));
        // 读取第一张盘的文件
        readFiles();
      } else {
        dispatch(detachUsb());
      }
    });
  }, [dispatch, readFiles]);
};

export default useUsbMount;
