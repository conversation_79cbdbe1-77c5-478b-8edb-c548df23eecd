import md5 from "md5";

const AppConfig = {
  appid: "63687F0B5DF1D202F955F24E44CFFE67",
  secret: "B2951632A6058DDC3A1AF7AEA777B32C",
};

/**
 * 生成签名
 */
const secretMd5 = () => {
  const timestamp = Math.floor(Date.now() / 1000); // 秒级时间戳
  const raw = `${timestamp}${AppConfig.secret}`;
  const signature = md5(raw).toUpperCase(); // 有些接口要求大写 MD5
  return {
    timestamp,
    signature,
    appid: AppConfig.appid,
  };
};

export default AppConfig;
export { secretMd5 };
