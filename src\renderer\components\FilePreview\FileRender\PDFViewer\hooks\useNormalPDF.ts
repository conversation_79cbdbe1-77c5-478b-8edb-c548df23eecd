import { useCallback, useEffect, useRef, useState } from "react";
import usePDFCore from "./usePDFCore";

export const useAllPagesPDFViewer = (filePath: string, initialScale = 1.0) => {
  const { pdfDocument, totalPages, scale, error, cancelAllTasks } = usePDFCore(
    filePath,
    initialScale
  );

  // 所有页面
  const [pages, setPages] = useState<(null | string)[]>([]);

  // 初始化页面数组
  useEffect(() => {
    if (!pdfDocument) return;
    setPages(Array(pdfDocument.numPages).fill(null));
  }, [pdfDocument]);

  // 是否渲染完成
  const [loading, setLoading] = useState(true);

  // 取消标志
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * 取消渲染任务
   */
  const cancelTasks = useCallback(() => {
    cancelAllTasks();
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, [cancelAllTasks]);

  /**
   * 渲染所有页面
   */
  const renderAllPages = useCallback(async () => {
    if (!pdfDocument) return;
    setLoading(true);

    // 创建新的AbortController
    abortControllerRef.current = new AbortController();

    const result: { [page: number]: string } = {};

    for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
      // 检查是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        console.log("渲染所有页面被取消");
        return;
      }

      const page = await pdfDocument.getPage(pageNum);

      const viewport = page.getViewport({ scale: scale * 3 });
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");

      if (!context) continue;

      // 设置 canvas 像素尺寸
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      await page.render({
        canvasContext: context,
        viewport,
      }).promise;

      result[pageNum] = canvas.toDataURL();
      setPages(prev => {
        const newPages = [...prev];
        newPages[pageNum - 1] = result[pageNum];
        return newPages;
      });
    }

    setLoading(false);
  }, [pdfDocument, scale, setPages]);

  useEffect(() => {
    if (!pdfDocument) return;
    setTimeout(renderAllPages, 300);
  }, [pdfDocument, renderAllPages]);

  // 清理函数
  useEffect(() => {
    return () => {
      cancelTasks();
      setPages([]);
      setLoading(true);
    };
  }, [cancelTasks]);

  return {
    pdfDocument,
    totalPages,
    scale,
    loading,
    error,
    pages,
    cancelTasks,
  };
};
