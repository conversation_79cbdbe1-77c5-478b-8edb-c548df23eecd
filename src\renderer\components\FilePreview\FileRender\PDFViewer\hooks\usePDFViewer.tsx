import * as pdfjsLib from "pdfjs-dist";
import { useCallback, useEffect, useRef, useState } from "react";

export interface PageInfo {
  pageNumber: number;
  width: number;
  height: number;
  scale: number;
}

export type DisplayMode = "single" | "all";

export interface UsePDFViewerProps {
  filePath: string;
  fileName: string;
  initialScale?: number;
}

export const usePDFViewer = ({
  filePath,
  fileName,
  initialScale = 1.0,
}: UsePDFViewerProps) => {
  // PDF文档
  const [pdfDocument, setPdfDocument] =
    useState<pdfjsLib.PDFDocumentProxy | null>(null);
  // 当前页码
  const [currentPage, setCurrentPage] = useState(1);
  // 总页数
  const [totalPages, setTotalPages] = useState(0);
  // 缩放比例
  const [scale, setScale] = useState(initialScale);
  // 是否加载中
  const [loading, setLoading] = useState(true);
  // 错误信息
  const [error, setError] = useState<string | null>(null);
  // 页面信息
  const [pageInfo, setPageInfo] = useState<PageInfo | null>(null);
  // 缩略图
  const [thumbnails, setThumbnails] = useState<{ [key: number]: string }>({});
  // 显示模式
  const [displayMode, setDisplayMode] = useState<DisplayMode>("all");
  // 是否渲染完成
  const [allPagesRendered, setAllPagesRendered] = useState(false);
  // 渲染的页面
  const [renderedPages, setRenderedPages] = useState<{ [key: number]: string }>(
    {}
  );
  // 渲染任务
  const renderTaskRef = useRef<pdfjsLib.RenderTask | null>(null);
  // 加载任务
  const loadingTaskRef = useRef<pdfjsLib.PDFDocumentLoadingTask | null>(null);
  // 取消标志
  const abortControllerRef = useRef<AbortController | null>(null);

  // 取消所有正在进行的任务
  const cancelAllTasks = useCallback(() => {
    // 取消渲染任务
    if (renderTaskRef.current) {
      renderTaskRef.current.cancel();
      renderTaskRef.current = null;
    }

    // 取消加载任务
    if (loadingTaskRef.current) {
      loadingTaskRef.current.destroy();
      loadingTaskRef.current = null;
    }

    // 取消AbortController
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // 加载PDF文档
  const loadPDF = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      setRenderedPages({});
      setAllPagesRendered(false);

      // 取消之前的任务
      cancelAllTasks();

      // 创建新的AbortController
      abortControllerRef.current = new AbortController();

      const loadingTask = pdfjsLib.getDocument(filePath);
      loadingTaskRef.current = loadingTask;

      const pdf = await loadingTask.promise;

      // 检查是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      setPdfDocument(pdf);
      setTotalPages(pdf.numPages);
      setCurrentPage(1);

      console.log(`PDF加载成功: ${fileName}, 总页数: ${pdf.numPages}`);
    } catch (err) {
      // 如果是取消操作，不显示错误
      if (abortControllerRef.current?.signal.aborted) {
        console.log("PDF加载被取消");
        return;
      }
      console.error("PDF加载失败:", err);
      setError(err instanceof Error ? err.message : "PDF文件加载失败");
    } finally {
      setLoading(false);
    }
  }, [filePath, fileName, cancelAllTasks]);

  // 渲染单个页面到canvas
  const renderPageToCanvas = useCallback(
    async (
      pageNumber: number,
      targetScale?: number
    ): Promise<string | null> => {
      if (!pdfDocument) return null;

      try {
        const page = await pdfDocument.getPage(pageNumber);
        const viewport = page.getViewport({ scale: targetScale || scale });

        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");

        if (!context) {
          throw new Error("无法获取canvas上下文");
        }

        // 设置canvas尺寸
        canvas.width = viewport.width;
        canvas.height = viewport.height;

        // 渲染页面
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        await page.render(renderContext).promise;

        return canvas.toDataURL();
      } catch (err) {
        console.error(`页面 ${pageNumber} 渲染失败:`, err);
        return null;
      }
    },
    [pdfDocument, scale]
  );

  // 渲染页面（单页模式）
  const renderPage = useCallback(
    async (
      pageNumber: number,
      targetScale?: number,
      canvas?: HTMLCanvasElement
    ) => {
      if (!pdfDocument || !canvas) return;

      try {
        const page = await pdfDocument.getPage(pageNumber);
        const context = canvas.getContext("2d");

        if (!context) {
          throw new Error("无法获取canvas上下文");
        }

        const viewport = page.getViewport({ scale: targetScale || scale });

        // 设置canvas尺寸
        canvas.width = viewport.width;
        canvas.height = viewport.height;

        // 更新页面信息
        setPageInfo({
          pageNumber,
          width: viewport.width,
          height: viewport.height,
          scale: targetScale || scale,
        });

        // 取消上一次渲染
        if (renderTaskRef.current) {
          renderTaskRef.current.cancel();
        }

        // 渲染页面
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        const renderTask = page.render(renderContext);
        renderTaskRef.current = renderTask;

        await renderTask.promise;

        renderTaskRef.current = null; // 渲染完成后清空
        console.log(`页面 ${pageNumber} 渲染完成`);
      } catch (err) {
        if (err?.name === "RenderingCancelledException") {
          // 被主动取消，不算错误
          return;
        }
        console.error(`页面 ${pageNumber} 渲染失败:`, err);
        setError(err instanceof Error ? err.message : "页面渲染失败");
      }
    },
    [pdfDocument, scale]
  );

  // 渲染所有页面
  const renderAllPages = useCallback(async () => {
    if (!pdfDocument) return;

    try {
      setAllPagesRendered(false);
      const newRenderedPages: { [key: number]: string } = {};

      // 批量渲染所有页面
      for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
        // 检查是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          console.log("渲染所有页面被取消");
          return;
        }

        const dataUrl = await renderPageToCanvas(pageNum);
        if (dataUrl) {
          newRenderedPages[pageNum] = dataUrl;
          // 更新状态以显示进度
          setRenderedPages(prev => ({ ...prev, [pageNum]: dataUrl }));
        }
      }

      setRenderedPages(newRenderedPages);
      setAllPagesRendered(true);
      console.log(`所有页面渲染完成，共 ${totalPages} 页`);
    } catch (err) {
      console.error("渲染所有页面失败:", err);
      setError("渲染所有页面失败");
    }
  }, [pdfDocument, totalPages, renderPageToCanvas]);

  // 生成缩略图
  const generateThumbnail = useCallback(
    async (pageNumber: number) => {
      if (!pdfDocument || thumbnails[pageNumber]) return;

      try {
        const page = await pdfDocument.getPage(pageNumber);
        const viewport = page.getViewport({ scale: 0.2 }); // 缩略图使用较小缩放

        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");

        if (!context) return;

        canvas.width = viewport.width;
        canvas.height = viewport.height;

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        await page.render(renderContext).promise;

        const thumbnailUrl = canvas.toDataURL();
        setThumbnails(prev => ({ ...prev, [pageNumber]: thumbnailUrl }));
      } catch (err) {
        console.error(`缩略图生成失败 (页面 ${pageNumber}):`, err);
      }
    },
    [pdfDocument, thumbnails]
  );

  // 页面导航
  const goToPage = useCallback(
    (pageNumber: number) => {
      if (pageNumber >= 1 && pageNumber <= totalPages) {
        setCurrentPage(pageNumber);
      }
    },
    [totalPages]
  );

  const goToPreviousPage = useCallback(() => {
    goToPage(currentPage - 1);
  }, [currentPage, goToPage]);

  const goToNextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [currentPage, goToPage]);

  // 缩放控制
  const zoomIn = useCallback(() => {
    setScale(prev => Math.min(prev * 1.2, 5.0));
  }, []);

  const zoomOut = useCallback(() => {
    setScale(prev => Math.max(prev / 1.2, 0.1));
  }, []);

  const fitToWidth = useCallback(
    (containerWidth: number) => {
      if (!pageInfo) return;

      const newScale = containerWidth / pageInfo.width;
      setScale(Math.min(newScale, 3.0));
    },
    [pageInfo]
  );

  const fitToHeight = useCallback(
    (containerHeight: number) => {
      if (!pageInfo) return;

      const newScale = containerHeight / pageInfo.height;
      setScale(Math.min(newScale, 3.0));
    },
    [pageInfo]
  );

  const resetZoom = useCallback(() => {
    setScale(1.0);
  }, []);

  // 切换显示模式
  const toggleDisplayMode = useCallback(() => {
    const newMode = displayMode === "single" ? "all" : "single";
    setDisplayMode(newMode);

    if (newMode === "all" && !allPagesRendered) {
      renderAllPages();
    }
  }, [displayMode, allPagesRendered, renderAllPages]);

  // 加载PDF文档
  useEffect(() => {
    loadPDF();
  }, [loadPDF]);

  // 当PDF文档加载完成后，自动开始渲染所有页面
  useEffect(() => {
    if (pdfDocument && displayMode === "all" && !allPagesRendered) {
      renderAllPages();
    }
  }, [pdfDocument, displayMode, allPagesRendered, renderAllPages]);

  // 生成缩略图
  useEffect(() => {
    if (pdfDocument) {
      // 生成当前页面和相邻页面的缩略图
      const pagesToGenerate = [currentPage];
      if (currentPage > 1) pagesToGenerate.push(currentPage - 1);
      if (currentPage < totalPages) pagesToGenerate.push(currentPage + 1);

      pagesToGenerate.forEach(pageNum => {
        if (!thumbnails[pageNum]) {
          generateThumbnail(pageNum);
        }
      });
    }
  }, [pdfDocument, currentPage, totalPages, thumbnails, generateThumbnail]);

  // 清理函数
  useEffect(() => {
    return () => {
      cancelAllTasks();
    };
  }, [cancelAllTasks]);

  return {
    // 状态
    pdfDocument,
    currentPage,
    totalPages,
    scale,
    loading,
    error,
    pageInfo,
    thumbnails,
    displayMode,
    allPagesRendered,
    renderedPages,

    // 方法
    loadPDF,
    renderPage,
    renderAllPages,
    renderPageToCanvas,
    generateThumbnail,
    goToPage,
    goToPreviousPage,
    goToNextPage,
    zoomIn,
    zoomOut,
    fitToWidth,
    fitToHeight,
    resetZoom,
    toggleDisplayMode,
    cancelAllTasks,
  };
};
