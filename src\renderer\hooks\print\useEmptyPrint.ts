import { addPublicPrefix } from "@/main/utils/path";
import { useAppSelector } from "@/renderer/store/hooks";
import { pickPageFiles } from "@/renderer/store/slice/client";
import { getDocument } from "pdfjs-dist";
import { useCallback } from "react";

const useEmptyPrint = () => {
  const emptyFile = useAppSelector(pickPageFiles);

  const emptyPrint = useCallback(() => {
    // todo:目前解析远程word，打印空白模版
    emptyFile?.files.map(async item => {
      const { outputPath } = await window.electronAPI.libreOffice.convertToPdf(
        addPublicPrefix(item.path, { remote: true })
      );
      if (outputPath) {
        const pdf = await getDocument(
          addPublicPrefix(outputPath, { remote: "ocx" })
        ).promise;
        console.log(pdf.numPages, "pdf.numPages");
        await window.electronAPI.util.print(outputPath, true, pdf.numPages);
      }
    });
  }, [emptyFile]);

  return {
    emptyPrint,
  };
};

export default useEmptyPrint;
