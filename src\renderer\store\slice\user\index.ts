import {
  createSlice,
  type Action,
  type PayloadAction,
  type SliceCaseReducers,
} from "@reduxjs/toolkit";
import type { UserState } from "./types";

const userSlice = createSlice<
  UserState,
  SliceCaseReducers<UserState>,
  "user",
  Action<any>
>({
  name: "user",
  initialState: {
    password: "123456",
  },
  reducers: {
    /** 设置打印密码 */
    setPassword: (state, action: PayloadAction<string>) => {
      state.password = action.payload;
    },
  },
});

export const { setMid, setTid } = userSlice.actions;

export default userSlice.reducer;
