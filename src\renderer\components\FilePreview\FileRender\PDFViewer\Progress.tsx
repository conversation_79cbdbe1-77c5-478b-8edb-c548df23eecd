import LoadingSpinner from "@/renderer/components/LoadingSpinner";
import { AnimatePresence, motion } from "motion/react";
import React from "react";

interface ProgressProps {
  /** 是否显示 */
  visible: boolean;
  /** 已渲染的页面 */
  renderedPages: number;
  /** 总页数 */
  totalPages: number;
}

const Progress: React.FC<ProgressProps> = ({
  visible,
  renderedPages = 0,
  totalPages = Infinity,
}) => {
  const showProgress = renderedPages > 0;

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          className="absolute inset-0 flex items-center justify-center z-10 w-full h-full bg-white/10"
          initial={{ opacity: 0.5 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0.5 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <LoadingSpinner
            text={
              <div className="flex flex-col items-center justify-center">
                {showProgress ? (
                  <>
                    <p className="text-sm">
                      已渲染 {renderedPages} / {totalPages} 页
                    </p>
                    <div className="w-64 bg-gray-200 rounded-full h-2 mt-2 overflow-hidden">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${(renderedPages / totalPages) * 100}%`,
                        }}
                      />
                    </div>
                  </>
                ) : (
                  <p className="text-sm">正在渲染所有页面...</p>
                )}
              </div>
            }
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Progress;
