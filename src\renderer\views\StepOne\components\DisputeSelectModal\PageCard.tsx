import { useAppSelector } from "@/renderer/store/hooks";
import {
  disputeBookImages,
  getFormThumbnail,
} from "@/renderer/utils/mapping/formMapping";
import { DisputePageType } from "@/types/views/dispute";
import type React from "react";
import { twMerge } from "tailwind-merge";

type Props = {
  active: DisputePageType;
  onClick: (type: DisputePageType) => void;
  /** 当前选中的纠纷类型代码 */
  disputeTypeCode?: string;
};

const PageCard: React.FC<Props> = ({ active, onClick, disputeTypeCode }) => {
  const activeDispute = useAppSelector(state => state.client?.activeDispute);

  // 如果没有纠纷类型代码，使用默认图片
  const getThumbnailImage = (pageType: DisputePageType) => {
    if (!disputeTypeCode) {
      // 返回默认图片或空字符串
      return "";
    }

    const formType =
      pageType === DisputePageType.LAWSUIT
        ? DisputePageType.LAWSUIT
        : DisputePageType.RESPONSE;
    return getFormThumbnail(disputeTypeCode as any, formType);
  };

  return (
    <div className="absolute w-full top-[6.5vw] flex justify-center gap-[4vw]">
      {activeDispute?.map(item => (
        <Card
          key={item.id}
          active={active === item.type_alias}
          type={item.type_alias}
          onClick={() => onClick(item.type_alias)}
          image={getThumbnailImage(item.type_alias)}
        />
      ))}
    </div>
  );
};

export default PageCard;

const Card = ({
  active,
  onClick,
  type,
  image,
}: {
  active: boolean;
  onClick: () => void;
  type: DisputePageType;
  image: string;
}) => {
  const classNames = twMerge(
    "relative w-[16.15vw] h-[23.44vw] rounded-[.3vw] border-[.1vw] border-solid bg-[#113769] cursor-pointer",
    active ? "border-[#00b1f3]" : "border-[#2f61a3]"
  );

  return (
    <div className={classNames} onClick={onClick}>
      <img
        src={disputeBookImages[type][active ? "active" : "default"]}
        alt="lawsuit"
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-[4.17vw]"
        draggable={false}
      />
      <div className="py-[0.78vw] px-[.57vw] w-full h-full">
        {image ? (
          <img
            src={image}
            alt="dispute"
            className="w-full h-full bg-white"
            draggable={false}
          />
        ) : (
          <div className="w-full h-full bg-white flex items-center justify-center text-gray-400 text-[0.8vw]">
            暂无预览
          </div>
        )}
      </div>
    </div>
  );
};
