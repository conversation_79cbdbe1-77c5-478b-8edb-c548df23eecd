import { ipcManager } from "@/main/ipc/IpcManager";
import { registerUsbProtocolHandler } from "@/main/protocols/usbProtocols";
import { IPC_CHANNELS } from "@/shared/constants";
import type { UsbFileInfo, UsbInfo } from "@/types/services/usbService";
import * as drivelist from "drivelist";
import { BrowserWindow, dialog } from "electron";
import * as fs from "fs";
import * as mammoth from "mammoth";
import path from "path";
import * as pdfParse from "pdf-parse";
import { usb } from "usb";

/**
 * USB服务
 * 检测U盘是否插入、读取文件信息、处理文件
 */
class UsbService {
  /** 单例 */
  private static instance: UsbService;
  /** U盘列表 */
  private usbList: UsbInfo[] = [];
  /** 当前U盘路径 */
  private currentUsbPath: string | null = null;
  /** 支持读取的文件 */
  private supportedExtensions = [".pdf", ".doc", ".docx", ".jpg", ".png"];
  /** 选中的文件 */
  private selectedFiles: Set<string> = new Set();
  /** 是否正在轮询 */
  private isPolling = false;

  /** 所有文件 */
  private _allFiles: UsbFileInfo[] = [];

  private constructor() {
    registerUsbProtocolHandler();
    this.detectUsbDevice();
    this.setupIpcHandlers();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): UsbService {
    if (!UsbService.instance) {
      UsbService.instance = new UsbService();
    }
    return UsbService.instance;
  }

  private setupIpcHandlers(): void {
    // 渲染端加载完毕时，获取U盘列表
    ipcManager.registerHandler(IPC_CHANNELS.USB.USB_RENDER_READY, async () => {
      return await this.notifyUsbRenderReady();
    });

    // 读取U盘文件
    ipcManager.registerHandler(IPC_CHANNELS.USB.READ_FILES, async () => {
      const files = await this.readFiles();
      return files;
    });

    // 选择U盘路径
    ipcManager.registerHandler(IPC_CHANNELS.USB.SELECT_PATH, async () => {
      return await this.selectPath();
    });

    // 设置U盘路径
    ipcManager.registerHandler(
      IPC_CHANNELS.USB.SET_PATH,
      async (_, path: string) => {
        return await this.setPath(path);
      }
    );

    // 读取文件内容
    ipcManager.registerHandler(
      IPC_CHANNELS.USB.READ_FILE,
      async (_, filePath: string) => {
        return await this.readFile(filePath);
      }
    );

    // 获取文件信息
    ipcManager.registerHandler(
      IPC_CHANNELS.USB.GET_FILE_INFO,
      async (_, filePath: string) => {
        return await this.getFileInfo(filePath);
      }
    );

    // 选择/取消选择文件
    ipcManager.registerHandler(
      IPC_CHANNELS.USB.TOGGLE_FILE_SELECTION,
      async (_, fileId: string, selected: boolean) => {
        return this.toggleFileSelection(fileId, selected);
      }
    );

    // 获取选中的文件
    ipcManager.registerHandler(
      IPC_CHANNELS.USB.GET_SELECTED_FILES,
      async () => {
        return await this.getSelectedFiles();
      }
    );

    // 清空选择
    ipcManager.registerHandler(IPC_CHANNELS.USB.CLEAR_SELECTION, async () => {
      this.clearSelection();
      return true;
    });

    ipcManager.registerHandler(
      IPC_CHANNELS.USB.READ_PDF_FILE,
      async (_, filePath: string) => {
        return await this.readPdfFile(filePath);
      }
    );

    ipcManager.registerHandler(
      IPC_CHANNELS.USB.READ_DOCX_FILE,
      async (_, filePath: string) => {
        return await this.readDocxFile(filePath);
      }
    );
  }

  /**
   * 检测U盘设备的热插拔
   */
  private async detectUsbDevice() {
    usb.on("attach", device => {
      this.notifyUsbDevice(true, device);
    });

    usb.on("detach", device => {
      this.notifyUsbDevice(false, device);
    });
  }

  /**
   * 通知渲染端U盘设备变化
   */
  private async notifyUsbRenderReady() {
    await this.notifyUsbDevice(true, null);
    return {
      attach: this.usbList.length > 0,
      usbList: this.usbList,
    };
  }

  /**
   * 插入/拔除U盘设备
   * @param device
   */
  private async notifyUsbDevice(attach: boolean, _device: usb.Device | null) {
    const usbDrives = await this.pollUsbMountpoint(attach);
    this.usbList = usbDrives || [];

    // U盘/移动硬盘插入
    const isRemovableUsb = this.usbList.length > 0;

    if (attach && isRemovableUsb) {
      await this.setDefaultUsbPath(this.usbList);
    }

    // 广播通知所有窗口U盘设备变化
    // TODO：后续优化窗口类
    BrowserWindow.getAllWindows().forEach(win => {
      win.webContents.send(IPC_CHANNELS.USB.USB_CHANGE, {
        // 是否正在加载
        loading: this.isPolling,
        // 真实状态 USB设备(插入/拔出)例如:鼠标、键盘、U盘等
        real: attach,
        // 是否插入U盘
        attach: isRemovableUsb,
        // 驱动器列表
        usbList: this.usbList,
      });
    });
  }

  /**
   * 轮询U盘挂载点
   * 因为U盘插入后，需要一定时间才能被系统识别，所以需要轮询
   * 轮询100次，每次间隔100ms，如果100次都未找到U盘，则返回空数组
   * @returns 返回U盘挂载点列表
   */
  private async pollUsbMountpoint(attach: boolean) {
    const intervalMs = 100;
    const maxAttempts = 100;
    let attempts = 0;

    if (this.isPolling) {
      return;
    }

    this.isPolling = true;

    // 广播通知所有窗口U盘设备变化
    BrowserWindow.getAllWindows().forEach(win => {
      win.webContents.send(IPC_CHANNELS.USB.USB_CHANGE, {
        // 是否正在加载
        loading: this.isPolling,
      });
    });

    return new Promise<UsbInfo[]>(resolve => {
      const timer = setInterval(async () => {
        attempts++;
        const drives = await drivelist.list();
        const usbDrives = drives.filter(
          drive =>
            (drive.isUSB || drive.isRemovable) &&
            drive.mountpoints &&
            drive.mountpoints.length > 0 &&
            drive.busType.toLowerCase() === "usb"
        );

        const resolver = () => {
          clearInterval(timer);
          this.isPolling = false;
          resolve(usbDrives);
        };

        if (attach) {
          if (usbDrives.length > 0) {
            resolver();
          } else if (attempts >= maxAttempts) {
            resolver();
          }
        } else {
          resolver();
        }
      }, intervalMs);
    });
  }

  /**
   * 设置U盘路径
   * @param path 路径
   * @returns 返回是否成功
   */
  private async setDefaultUsbPath(usbDrives: UsbInfo[]) {
    // 如果检测到驱动器且当前没有设置路径，自动设置第一个
    if (usbDrives.length > 0 && !this.currentUsbPath) {
      const firstDrive = usbDrives[0];
      // 如果挂载点存在，则设置当前U盘路径
      if (firstDrive.mountpoints.length > 0) {
        this.currentUsbPath = firstDrive.mountpoints[0].path;
      }
    }
  }

  /**
   * 设置U盘路径
   */
  private async setPath(path: string) {
    try {
      if (fs.existsSync(path) && fs.statSync(path).isDirectory()) {
        this.currentUsbPath = path;
        return true;
      } else {
        console.error("路径不存在或不是目录:", path);
        return false;
      }
    } catch (error) {
      console.error("设置U盘路径失败:", error);
      return false;
    }
  }

  private isScanning = false;

  get allFiles() {
    return this.sortFiles(this._allFiles);
  }

  set allFiles(files: UsbFileInfo[]) {
    this._allFiles = files;
  }

  /**
   * 读取U盘文件
   */
  private async readFiles() {
    if (!this.currentUsbPath) {
      throw new Error("未设置U盘路径");
    }
    if (this.isScanning) {
      return this.allFiles;
    }

    this.isScanning = true;
    this.allFiles = [];

    try {
      if (this.allFiles.length === 0) {
        await this.scanDirectory(this.currentUsbPath, this.allFiles);
        this.isScanning = false;
      }

      // 设置文件的选中状态
      this.allFiles.forEach(file => {
        file.selected = this.selectedFiles.has(file.id);
      });
    } catch (error) {
      console.error("读取U盘文件失败:", error);
      throw error;
    }

    return this.allFiles;
  }

  /**
   * 递归扫描目录
   * @param dirPath 目录路径
   * @param files 文件列表
   * @param currentDepth 当前深度
   * @param maxDepth 最大深度
   */
  private async scanDirectory(
    dirPath: string,
    files: UsbFileInfo[],
    currentDepth = 0,
    maxDepth?: number
  ): Promise<void> {
    if (maxDepth && currentDepth >= maxDepth) return;

    try {
      const items = await fs.promises.readdir(dirPath);

      await Promise.all(
        items.map(async item => {
          const itemPath = path.join(dirPath, item);

          try {
            const stats = await fs.promises.stat(itemPath);

            if (stats.isDirectory()) {
              await this.scanDirectory(
                itemPath,
                files,
                currentDepth + 1,
                maxDepth
              );
            } else {
              // 获取文件扩展名
              const ext = path.extname(item).toLowerCase();
              if (this.supportedExtensions.includes(ext)) {
                const fileInfo: UsbFileInfo = {
                  id: this.generateFileId(itemPath),
                  name: item,
                  path: itemPath,
                  size: stats.size,
                  type: this.getFileType(ext),
                  extension: ext,
                  lastModified: stats.mtime.toISOString(),
                  selected: false,
                };
                files.push(fileInfo);
              }
            }
          } catch (error) {
            console.error(`无法访问文件 ${itemPath}:`, error);
          }
        })
      );
    } catch (error) {
      console.error("扫描目录失败:", error);
    }
  }

  /**
   * 切换文件选择状态
   * @param fileId 文件ID
   * @param selected 是否选中
   * @returns 是否成功
   */
  private toggleFileSelection(fileId: string, selected: boolean) {
    try {
      if (selected) {
        this.selectedFiles.add(fileId);
      } else {
        this.selectedFiles.delete(fileId);
      }
      return true;
    } catch (error) {
      console.error("切换文件选择状态失败:", error);
      return false;
    }
  }

  /**
   * 获取选中的文件
   */
  private async getSelectedFiles() {
    try {
      const allFiles = await this.readFiles();
      return allFiles.filter(file => this.selectedFiles.has(file.id));
    } catch (error) {
      console.error("获取选中的文件失败:", error);
      return [];
    }
  }

  /**
   * 清空选择
   */
  private clearSelection(): void {
    this.selectedFiles.clear();
  }

  /**
   * 选择U盘路径
   * @returns 选择的U盘路径
   */
  private async selectPath() {
    const result = await dialog.showOpenDialog({
      title: "选择U盘路径",
      properties: ["openDirectory"],
      message: "请选择U盘根目录",
    });

    if (!result.canceled && result.filePaths.length > 0) {
      this.currentUsbPath = result.filePaths[0];
      return this.currentUsbPath;
    }

    return null;
  }

  /**
   * 读取文件内容
   * @param filePath 文件路径
   * @returns 文件内容
   */
  private async readFile(filePath: string) {
    try {
      return fs.readFileSync(filePath);
    } catch (error) {
      console.error("读取文件内容失败:", error);
      throw error;
    }
  }

  /**
   * 获取文件信息
   */
  private async getFileInfo(filePath: string): Promise<UsbFileInfo | null> {
    try {
      const stats = fs.statSync(filePath);
      const ext = path.extname(filePath).toLowerCase();

      return {
        id: this.generateFileId(filePath),
        name: path.basename(filePath),
        path: filePath,
        size: stats.size,
        type: this.getFileType(ext),
        extension: ext,
        lastModified: stats.mtime.toISOString(),
        selected: false,
      };
    } catch (error) {
      console.error("获取文件信息失败:", error);
      return null;
    }
  }

  /**
   * 读取PDF文件
   * @param filePath PDF文件路径或Buffer
   * @returns PDF文件内容
   */
  private async readPdfFile(filePath: string | Buffer) {
    let buffer: Buffer;

    if (typeof filePath === "string") {
      // 检测文件是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error("文件不存在");
      }

      buffer = fs.readFileSync(filePath);
    } else {
      buffer = filePath;
    }

    try {
      return await pdfParse.default(buffer);
    } catch (error) {
      console.error("读取PDF文件失败:", error);
      throw error;
    }
  }

  /**
   * 读取DOCX文件
   * @param filePath DOCX文件路径或Buffer
   * @returns DOCX文件内容
   */
  private async readDocxFile(filePath: string | Buffer) {
    let buffer: Buffer;

    if (typeof filePath === "string") {
      // 检测文件是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error("文件不存在");
      }

      buffer = fs.readFileSync(filePath);
    } else {
      buffer = filePath;
    }

    try {
      return await mammoth.extractRawText({ buffer });
    } catch (error) {
      console.error("读取DOCX文件失败:", error);
      throw error;
    }
  }

  /**
   * 获取当前U盘路径
   */
  public getCurrentUsbPath(): string | null {
    return this.currentUsbPath;
  }

  /**
   * 设置当前U盘路径
   */
  public setCurrentUsbPath(path: string): void {
    this.currentUsbPath = path;
  }

  /**
   * 获取选中文件数量
   */
  public getSelectedFileCount(): number {
    return this.selectedFiles.size;
  }

  /**
   * =========================================
   * 工具函数
   * =========================================
   */

  /**
   * 排序文件
   * @param files 文件列表
   * @returns 排序后的文件列表
   */
  private sortFiles(files: UsbFileInfo[]) {
    const typeOrder = ["pdf", "word", "image"];

    return files.sort((a, b) => {
      const typeIndexA = typeOrder.indexOf(a.type);
      const typeIndexB = typeOrder.indexOf(b.type);

      // 如果类型不同，按类型优先级排序
      if (typeIndexA !== typeIndexB) {
        return typeIndexA - typeIndexB;
      }

      // 类型相同，则按名称排序
      return a.name.localeCompare(b.name);
    });
  }

  /**
   * 生成文件ID
   * @param filePath 文件路径
   */
  private generateFileId(filePath: string): string {
    return Buffer.from(filePath)
      .toString("base64")
      .replace(/[^a-zA-Z0-9]/g, "");
  }

  /**
   * 获取文件类型
   * @param extension 文件扩展名
   */
  private getFileType(extension: string) {
    switch (extension) {
      case ".pdf":
        return "pdf";
      case ".doc":
      case ".docx":
        return "word";
      case ".xls":
      case ".xlsx":
        return "excel";
      case ".ppt":
      case ".pptx":
        return "ppt";
      case ".txt":
        return "txt";
      case ".jpg":
      case ".jpeg":
      case ".png":
        return "image";
      default:
        return "other";
    }
  }
}

export default UsbService;
