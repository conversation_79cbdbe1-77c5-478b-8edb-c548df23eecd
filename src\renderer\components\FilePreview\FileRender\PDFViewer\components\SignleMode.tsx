import LoadingSpinner from "@/renderer/components/LoadingSpinner";
import { useEffect, useRef } from "react";
import { useSinglePagePDFViewer } from "../hooks/useSignlePDF";
import ThumbnailSide from "./ThumbnailSide";

interface SingleModeProps {
  /** 是否显示缩略图 */
  showThumbnails: boolean;
  /** 文件路径 */
  filePath: string;
  /** 初始缩放比例 */
  initialScale?: number;
  /** 设置当前页码 */
  onPageChange?: (page: number) => void;
  /** 设置总页数 */
  setTotalPages: (pages: number) => void;
  /** 当前页码 */
  currentPage: number;
}

const SingleMode: React.FC<SingleModeProps> = ({
  showThumbnails,
  filePath,
  initialScale = 1.0,
  onPageChange,
  setTotalPages,
  currentPage,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const {
    thumbnails,
    totalPages,
    loading,
    currentPage: currentPageViewer,
    goToPage,
    renderPage,
  } = useSinglePagePDFViewer(filePath, initialScale);

  useEffect(() => {
    if (canvasRef.current) {
      renderPage(currentPage, canvasRef.current);
    }
  }, [currentPage, currentPageViewer, renderPage]);

  /**
   * 设置总页数
   */
  useEffect(() => {
    setTotalPages(totalPages);
  }, [totalPages, setTotalPages]);

  return (
    <div className="flex-1 flex overflow-hidden">
      {/* 缩略图侧边栏 */}
      <ThumbnailSide
        visible={showThumbnails}
        totalPages={totalPages}
        thumbnails={thumbnails}
        currentPage={currentPage}
        goToPage={page => {
          goToPage(page);
          onPageChange?.(page);
        }}
      />
      <div className="flex-1 overflow-auto p-4 bg-gray-100">
        <div className="flex justify-center">
          <canvas
            ref={canvasRef}
            className="shadow-lg bg-white"
            style={{
              maxWidth: "100%",
              height: "auto",
            }}
          />
          <LoadingSpinner show={loading} />
        </div>
      </div>
    </div>
  );
};

export default SingleMode;
