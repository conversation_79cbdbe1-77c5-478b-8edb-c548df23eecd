import { RequestConfig } from "./types";

/**
 * 默认请求配置
 */
export const DEFAULT_CONFIG: RequestConfig = {
  // 请求超时时间
  timeout: 30000,
  // 请求基础URL
  baseURL:
    process.env.NODE_ENV === "development"
      ? import.meta.env.VITE_API_URL
      : "/api",
  // 请求头
  headers: {
    "Content-Type": "application/json",
  },
  // 是否显示加载状态
  showLoading: true,
  // 是否显示错误提示
  showError: true,
  // 是否需要token认证
  needAuth: true,
  // 重试次数
  retryCount: 2,
  // 响应类型
  responseType: "json",
  // 跨域请求时是否需要使用凭证
  withCredentials: false,
};

/**
 * 请求状态码配置
 */
export enum STATUS_CODE {
  // 成功
  SUCCESS = 0,
  FAILED = 1,
  // 登录状态失效返回1001
  UNAUTHORIZED = 1001,
  // UNAUTHORIZED = 401,
  // FORBIDDEN = 403,
  NOT_FOUND = 404,
  // INTERNAL_SERVER_ERROR = 500,
  // BAD_GATEWAY = 502,
  // SERVICE_UNAVAILABLE = 503,
  // GATEWAY_TIMEOUT = 504,
}

/**
 * 错误信息配置
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: "网络连接失败，请检查网络设置",
  TIMEOUT_ERROR: "请求超时，请稍后重试",
  UNAUTHORIZED: "登录已过期，请重新登录",
  FORBIDDEN: "没有权限访问该资源",
  NOT_FOUND: "请求的资源不存在",
  INTERNAL_SERVER_ERROR: "服务器内部错误",
  BAD_GATEWAY: "网关错误",
  SERVICE_UNAVAILABLE: "服务暂时不可用",
  GATEWAY_TIMEOUT: "网关超时",
  CANCEL_REQUEST: "请求已取消",
  UNKNOWN_ERROR: "未知错误",
} as const;

/**
 * 需要重试的状态码
 */
export const RETRY_STATUS_CODES: STATUS_CODE[] = [STATUS_CODE.NOT_FOUND];

/**
 * 重试延迟时间（毫秒）
 */
export const RETRY_DELAY = 1000;

/**
 * 最大重试次数
 */
export const MAX_RETRY_COUNT = 3;
