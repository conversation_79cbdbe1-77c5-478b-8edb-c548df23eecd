import { LIBRE_OFFICE_CHANNELS } from "./libreOffice";
import { LOCK_CHANNELS } from "./lock";
import { OCX_CHANNELS } from "./ocx";
import { USB_CHANNELS } from "./usb";
import { UTIL_CHANNELS } from "./utils";
import { WINDOW_CHANNELS } from "./window";

/**
 * IPC 通道常量定义
 */
export const IPC_CHANNELS = {
  // 窗口控制
  WINDOW: WINDOW_CHANNELS,
  // USB 操作
  USB: USB_CHANNELS,
  // 工具
  UTIL: UTIL_CHANNELS,
  // LibreOffice 操作
  LIBRE_OFFICE: LIBRE_OFFICE_CHANNELS,
  // 高拍仪
  OCX: OCX_CHANNELS,
  // 锁相关
  LOCK: LOCK_CHANNELS,
} as const;

// 导出类型
export type IpcChannels = typeof IPC_CHANNELS;
