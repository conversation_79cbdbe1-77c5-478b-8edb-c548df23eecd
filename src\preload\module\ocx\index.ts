import { IPC_CHANNELS } from "@/shared/constants";
import { ipc<PERSON><PERSON><PERSON> } from "electron";
import type { Ocx<PERSON><PERSON> } from "./types";

const ocxAPI: OcxAPI = {
  startPlay: () => ipcRenderer.invoke(IPC_CHANNELS.OCX.START_PLAY),

  stopPlay: () => ipcRenderer.invoke(IPC_CHANNELS.OCX.STOP_PLAY),

  setMode: mode => ipcRenderer.invoke(IPC_CHANNELS.OCX.SET_MODE, mode),

  setVideoDispMode: mode =>
    ipcRenderer.invoke(IPC_CHANNELS.OCX.SET_VIDEO_DISP_MODE, mode),

  takePhoto: type => ipcRenderer.invoke(IPC_CHANNELS.OCX.TAKE_PHOTO, type),

  clearFolder: () => ipcRenderer.invoke(IPC_CHANNELS.OCX.CLEAR_FOLDER),

  onImageStream: callback =>
    ipcRenderer.on(IPC_CHANNELS.OCX.GET_IMAGE_STREAM, (_, data) => {
      callback(data);
    }),

  anyCommand: data => ipcRenderer.invoke(IPC_CHANNELS.OCX.ANY, data),
};

export default ocxAPI;
