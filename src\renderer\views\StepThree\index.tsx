import { useAppDispatch } from "@/renderer/store/hooks";
import { setIsAnalyzing } from "@/renderer/store/slice/client";
import circle from "@assets/step-three/circle.png";
import { motion } from "motion/react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import ProgressBar from "../../components/Progress";

const StepThree = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const start = Date.now();
    const duration = 10 * 1000;

    const timer = setInterval(() => {
      const elapsed = Date.now() - start;
      const percent = Math.min(100, Math.floor((elapsed / duration) * 100));
      setProgress(percent);

      if (elapsed >= duration) {
        clearInterval(timer);
        navigate("/dispute/step-four");
      }
    }, 100);

    dispatch(setIsAnalyzing(true));

    return () => clearInterval(timer);
  }, [dispatch, navigate]);

  return (
    <div className="flex flex-col items-center justify-center h-full">
      <motion.div className="relative">
        <div className="relative  rounded-full flex items-center justify-center">
          <div className="absolute w-[34vw] h-[34vw] rounded-full bg-[radial-gradient(#0e2951,transparent_100%)] blur-[100px] z-0" />
          <motion.img
            src={circle}
            alt="circle"
            className="w-[24vw] object-contain relative z-10"
            animate={{
              rotate: 360,
            }}
            transition={{
              rotate: { duration: 5, repeat: Infinity, ease: "linear" },
            }}
          />
        </div>
      </motion.div>

      <div className=" text-primary-70 flex flex-col items-center gap-[2.24vw]">
        <div className="flex flex-col items-center">
          <div className="font-ZQKLKT-Bold text-[2.4vw]">智能分析中</div>
          <div className="text-[1.04vw]">
            分析过程预计需要10秒钟，请耐心等待请勿离开
          </div>
        </div>
        <ProgressBar progress={progress} />
      </div>
      <div className="text-[1.46vw] font-ZQKLKT-Bold text-primary-70">
        {progress}%
      </div>
    </div>
  );
};

export default StepThree;
