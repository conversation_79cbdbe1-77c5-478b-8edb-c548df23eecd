export const USB_CHANNELS = {
  // 渲染端加载完毕时，获取已插入的U盘列表
  USB_RENDER_READY: "usb:usbRenderReady",
  // U盘设备插入/拔出
  USB_CHANGE: "usb:usbChange",
  // 读取U盘文件
  READ_FILES: "usb:readFiles",
  // 选择U盘路径
  SELECT_PATH: "usb:selectPath",
  // 设置U盘路径
  SET_PATH: "usb:setPath",
  // 读取文件
  READ_FILE: "usb:readFile",
  // 获取文件信息
  GET_FILE_INFO: "usb:getFileInfo",
  // 切换文件选择状态
  TOGGLE_FILE_SELECTION: "usb:toggleFileSelection",
  // 获取选中的文件
  GET_SELECTED_FILES: "usb:getSelectedFiles",
  // 上传选中的文件
  UPLOAD_SELECTED_FILES: "usb:uploadSelectedFiles",
  // 清空选择
  CLEAR_SELECTION: "usb:clearSelection",
  // 读取PDF文件
  READ_PDF_FILE: "usb:readPdfFile",
  // 读取DOCX文件
  READ_DOCX_FILE: "usb:readDocxFile",
};
