import { AnimatePresence, motion } from "motion/react";
import React from "react";
import { GhostButton } from "./Button";

interface Props {
  /** 是否显示 */
  visible?: boolean;
  onDelete?: () => void;
  onCancel?: () => void;
}

const DeleteConfim: React.FC<Props> = ({
  visible = true,
  onDelete,
  onCancel,
}) => {
  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{
            duration: 0.2,
            ease: "easeIn",
          }}
          className="absolute left-0 top-0 w-full h-full flex items-center justify-center"
        >
          <div className="w-[47.71vw] h-[8.54vw] z-10 flex items-center justify-center gap-[3.54vw] bg-mask/90 rounded-[.63vw]">
            <div className="font-ZQKLKT-Bold text-[2.4vw] text-white">
              是否确认删除该文件？
            </div>
            <div className="flex items-center gap-[1.46vw]">
              <GhostButton
                className="text-danger-default bg-red-400/10 border-danger-default"
                onClick={onDelete}
              >
                删除
              </GhostButton>
              <GhostButton onClick={onCancel}>取消</GhostButton>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DeleteConfim;
