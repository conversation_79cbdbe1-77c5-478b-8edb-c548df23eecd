import { useLibreOfficeStatus } from "@/renderer/hooks";
import { AlertTriangleIcon } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useState } from "react";
import LibreOfficeInstallGuide from "./LibreOfficeInstallGuide";

/**
 * LibreOffice 状态横幅组件
 * 在应用顶部显示 LibreOffice 安装状态
 */
const LibreOfficeStatusBanner: React.FC = () => {
  const { isAvailable, isChecking } = useLibreOfficeStatus();
  const [showInstallGuide, setShowInstallGuide] = useState(false);

  // 如果 LibreOffice 可用或正在检查，不显示横幅
  if (isAvailable === true || isChecking || isAvailable === null) {
    return null;
  }

  return (
    <>
      <AnimatePresence>
        <motion.div
          className="fixed top-0 left-0 right-0 z-50 bg-orange-500 text-white px-4 py-2 shadow-lg"
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center space-x-3">
              <AlertTriangleIcon className="w-6 h-6" color="white" />

              <div>
                <p className="text-sm font-medium">
                  需要安装 LibreOffice 以支持 Word 文档预览功能
                </p>
                <p className="text-xs opacity-90">点击下方按钮查看安装指南</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowInstallGuide(true)}
                className="px-3 py-1 text-xs bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors"
              >
                安装指南
              </button>
              <button
                onClick={() =>
                  window.open(
                    "https://www.libreoffice.org/download/download/",
                    "_blank"
                  )
                }
                className="px-3 py-1 text-xs bg-white text-orange-600 rounded hover:bg-gray-100 transition-colors"
              >
                立即下载
              </button>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* LibreOffice 安装指南 */}
      <LibreOfficeInstallGuide
        visible={showInstallGuide}
        onClose={() => setShowInstallGuide(false)}
      />
    </>
  );
};

export default LibreOfficeStatusBanner;
