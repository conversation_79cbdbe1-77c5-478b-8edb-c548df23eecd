import React from "react";

interface StatsBarProps {
  /** 文件名 */
  fileName: string;
  /** 显示模式 */
  displayMode: "single" | "all";
  /** 页面信息 */
  pageInfo?: { width: number; height: number };
  /** 总页数 */
  totalPages: number;
  /** 是否渲染完成 */
  allPagesRendered: boolean;
  /** 渲染的页面 */
  renderedPages: number;
}

const StatsBar: React.FC<StatsBarProps> = ({
  fileName,
  displayMode,
  pageInfo,
  totalPages,
  allPagesRendered,
  renderedPages,
}) => {
  return (
    <div className="px-3 py-2 text-xs text-gray-500 bg-gray-50 border-t">
      <div className="flex justify-between">
        <span>文件名: {fileName}</span>
        <div className="flex items-center space-x-4">
          <span>
            显示模式: {displayMode === "single" ? "单页" : "全部页面"}
          </span>
          {pageInfo && displayMode === "single" && (
            <span>
              页面尺寸: {Math.round(pageInfo.width)} ×{" "}
              {Math.round(pageInfo.height)} px
            </span>
          )}
          {displayMode === "all" && (
            <span>
              总页数: {totalPages} 页
              {!allPagesRendered && ` (渲染中: ${renderedPages}/${totalPages})`}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsBar;
