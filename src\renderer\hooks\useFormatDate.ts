import { useState, useEffect } from "react";

const timeRun = (
  cb: (
    dateStr: string,
    weekdayStr: string,
    hour: string,
    minute: string,
    second: string
  ) => void
) => {
  const now = new Date();

  // 格式化日期，如：2025-06-25
  const dateStr = now.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });

  // 获取星期几（0-6），然后转换为中文
  const weekdayStr = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
  ][now.getDay()];

  const hour = now.getHours();
  const minute = now.getMinutes();
  const second = now.getSeconds();

  const format = (num: number) => {
    return num < 10 ? `0${num}` : num.toString();
  };

  cb(dateStr, weekdayStr, format(hour), format(minute), format(second));

  requestAnimationFrame(() => {
    timeRun(cb);
  });
};

const useFormatDate = () => {
  const [dateStr, setDateStr] = useState("");
  const [weekdayStr, setWeekdayStr] = useState("");
  const [hour, setHour] = useState("");
  const [minute, setMinute] = useState("");
  const [second, setSecond] = useState("");

  useEffect(() => {
    timeRun((dateStr, weekdayStr, hour, minute, second) => {
      setDateStr(dateStr);
      setWeekdayStr(weekdayStr);
      setHour(hour);
      setMinute(minute);
      setSecond(second);
    });
  }, []);

  return {
    dateStr,
    weekdayStr,
    hour,
    minute,
    second,
  };
};

export default useFormatDate;
