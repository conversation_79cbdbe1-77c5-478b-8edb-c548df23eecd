import { useAppDispatch, useAppSelector } from "@/renderer/store/hooks";
import {
  clearError,
  fetchDisputeFormsV2,
  selectDisputeError,
  selectDisputeFormsV2,
  selectDisputeInitialized,
  selectDisputeLoading,
  updateFormPrintCount,
  updateFormUseCount,
  updatePagination,
} from "@/renderer/store/slice/disputeSlice";
import type { DisputeFormV2Payload } from "@/types/views/dispute";
import { useCallback } from "react";
import { useSelector } from "react-redux";

/**
 * 纠纷数据管理Hook
 * @param payload 请求参数
 * @param autoFetch 是否自动获取数据，默认为true
 * @returns 纠纷数据状态和操作方法
 */
export const useDispute = () => {
  const dispatch = useAppDispatch();

  const mid = useAppSelector(state => state.client?.mid);
  const pageSize = useAppSelector(state => state.dispute?.pagination.pageSize);
  const forms = useSelector(selectDisputeFormsV2);
  const loading = useSelector(selectDisputeLoading);
  const error = useSelector(selectDisputeError);
  const initialized = useSelector(selectDisputeInitialized);

  /**
   * 获取纠纷表单数据
   */
  const fetchForms = useCallback(
    (payload: Partial<DisputeFormV2Payload>) => {
      if (!mid) return;
      console.log(payload.limit, pageSize, 10);
      dispatch(updatePagination({ pageSize: payload.limit || pageSize || 10 }));
      dispatch(
        fetchDisputeFormsV2({
          ...payload,
          mid: payload.mid || mid,
          force: payload.force || false,
          append: payload.append || false,
          limit: payload.limit || pageSize,
        })
      );
    },
    [dispatch, mid, pageSize]
  );

  /**
   * 清除错误信息
   */
  const clearErrorHandler = () => {
    dispatch(clearError());
  };

  /**
   * 更新表单使用次数
   */
  const updateUseCount = (id: number, useCount: number) => {
    dispatch(updateFormUseCount({ id, useCount }));
  };

  /**
   * 更新表单打印次数
   */
  const updatePrintCount = (id: number, printCount: number) => {
    dispatch(updateFormPrintCount({ id, printCount }));
  };

  /**
   * 根据ID获取单个表单
   */
  const getFormById = (id: number) => {
    return forms.find((form: any) => form.id === id);
  };

  /**
   * 根据纠纷类型代码获取表单
   */
  const getFormByType = (intentType: string) => {
    return forms.find((form: any) => form.intent_type === intentType);
  };

  /**
   * 获取所有纠纷类型代码
   */
  const getAllIntentTypes = () => {
    return forms.map((form: any) => form.intent_type).filter(Boolean);
  };

  /**
   * 搜索纠纷表单
   */
  const searchForms = (keyword: string) => {
    if (!keyword.trim()) return forms;

    const lowerKeyword = keyword.toLowerCase();
    return forms.filter(
      form =>
        form.label.toLowerCase().includes(lowerKeyword) ||
        form.mark?.toLowerCase().includes(lowerKeyword) ||
        form.intent_type.toLowerCase().includes(lowerKeyword)
    );
  };

  return {
    forms,
    loading,
    error,
    initialized,

    fetchForms,
    clearError: clearErrorHandler,
    updateUseCount,
    updatePrintCount,

    getFormById,
    getFormByType,
    getAllIntentTypes,
    searchForms,
  };
};

export default useDispute;
