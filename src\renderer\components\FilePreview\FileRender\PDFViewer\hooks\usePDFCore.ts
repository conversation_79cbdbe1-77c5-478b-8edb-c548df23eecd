import * as pdfjsLib from "pdfjs-dist";
import { useCallback, useEffect, useRef, useState } from "react";

const usePDFCore = (filePath: string, initialScale = 1.0) => {
  // PDF文档
  const [pdfDocument, setPdfDocument] =
    useState<pdfjsLib.PDFDocumentProxy | null>(null);
  // 总页数
  const [totalPages, setTotalPages] = useState(0);
  // 缩放比例
  const [scale, setScale] = useState(initialScale);
  // 是否加载中
  const [loading, setLoading] = useState(true);
  // 错误信息
  const [error, setError] = useState<string | null>(null);
  // 当前页码
  const [currentPage, setCurrentPage] = useState(1);

  // 加载任务
  const loadingTaskRef = useRef<pdfjsLib.PDFDocumentLoadingTask | null>(null);
  // 取消标志
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * 取消所有任务
   */
  const cancelAllTasks = useCallback(() => {
    // 取消加载任务
    if (loadingTaskRef.current) {
      loadingTaskRef.current.destroy();
      loadingTaskRef.current = null;
    }

    // 取消AbortController
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  /**
   * 加载 PDF 文档
   * @param filePath - 文件路径
   * @param fileName - 文件名
   * @param initialScale - 初始缩放比例
   */
  const loadPDF = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // 取消之前的任务
      cancelAllTasks();

      // 创建新的AbortController
      abortControllerRef.current = new AbortController();

      const task = pdfjsLib.getDocument(filePath);
      loadingTaskRef.current = task;

      const pdf = await task.promise;

      // 检查是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        console.log("PDF加载被取消");
        return;
      }

      setPdfDocument(pdf);
      setTotalPages(pdf.numPages);
    } catch (err) {
      // 如果是取消操作，不显示错误
      if (abortControllerRef.current?.signal.aborted) {
        console.log("PDF加载被取消");
        return;
      }
      setError(err instanceof Error ? err.message : "加载失败");
    } finally {
      setLoading(false);
    }
  }, [filePath, cancelAllTasks]);

  /**
   * 跳转页码
   * @param page - 页码
   */
  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) setCurrentPage(page);
    },
    [totalPages]
  );

  /**
   * 放大
   */
  const zoomIn = useCallback(
    () => setScale(prev => Math.min(prev * 1.2, 5)),
    []
  );
  /**
   * 缩小
   */
  const zoomOut = useCallback(
    () => setScale(prev => Math.max(prev / 1.2, 0.1)),
    []
  );
  /**
   * 重置缩放
   */
  const resetZoom = useCallback(() => setScale(1.0), []);

  useEffect(() => {
    loadPDF();
  }, [loadPDF]);

  // 清理函数
  useEffect(() => {
    return () => {
      cancelAllTasks();
    };
  }, [cancelAllTasks]);

  return {
    pdfDocument,
    totalPages,
    currentPage,
    scale,
    loading,
    error,
    setError,
    goToPage,
    zoomIn,
    zoomOut,
    resetZoom,
    loadPDF,
    cancelAllTasks,
  };
};

export default usePDFCore;
