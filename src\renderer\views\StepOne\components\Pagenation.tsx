import { useDispute } from "@/renderer/hooks";
import { useAppSelector } from "@/renderer/store/hooks";
import next from "@assets/step-one/next.png";
import pagenationBg from "@assets/step-one/pagenation-bg.png";
import prev from "@assets/step-one/prev.png";
import { motion } from "motion/react";
import type React from "react";
import { twMerge } from "tailwind-merge";

const Pagenation: React.FC = () => {
  const pagination = useAppSelector(state => state.dispute?.pagination);
  const { fetchForms } = useDispute();
  const onPageChange = (page: number) => {
    fetchForms({ page, force: true });
  };

  return (
    <div className="w-full h-[4vw] relative flex-shrink-0">
      <img
        src={pagenationBg}
        alt="pagenation-bg"
        className="absolute z-0 top-0 left-0 w-full h-full"
      />

      <div className="absolute top-0 left-0 pl-[5vw] pr-[1vw] w-full h-full flex justify-between items-center text-primary-70 text-[1vw]">
        <div>
          已累计服务<span className="mx-2">{10000}</span>人次
        </div>

        <div className="flex items-center">
          共有<span className="mx-2">{pagination?.totalPage}</span>页 ，
          当前为第
          <span className="mx-2">{pagination?.current}</span>页
          {!!pagination?.totalPage && pagination?.totalPage > 1 && (
            <div className="flex items-center">
              <SplitLine className="mx-2" />

              {pagination.current > 1 && (
                <motion.img
                  src={prev}
                  alt="prev"
                  className="h-[3vw] object-contain cursor-pointer"
                  whileTap={{ scale: 0.9 }}
                  onClick={() => onPageChange?.(pagination.current - 1)}
                />
              )}
              {pagination.current < pagination.totalPage && (
                <motion.img
                  src={next}
                  alt="next"
                  className="h-[3vw] object-contain cursor-pointer"
                  whileTap={{ scale: 0.9 }}
                  onClick={() => onPageChange?.(pagination.current + 1)}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const SplitLine: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div
      className={twMerge("flex", className)}
      style={{
        height: "min(1.88vw, 3.33vh)",
        paddingInline: "min(1.57vw, 0.89vh)",
      }}
    >
      <div className="bg-[#184784] w-[2px] h-full"></div>
      <div className="bg-[#021735] w-[2px] h-full"></div>
    </div>
  );
};

export default Pagenation;
