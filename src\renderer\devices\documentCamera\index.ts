import { IPC_CHANNELS } from "@/shared/constants";
import type { <PERSON><PERSON>erWindow } from "electron";
import type {
  OCXCommand,
  OCXMessageType,
  OCXMode,
  OCXVideoDispMode,
} from "./types";

/**
 * 高拍仪 WebSocket 通信服务
 * @deprecated 现使用ocxService.ts
 */
class DocumentCameraService {
  /**
   * 窗口实例
   */
  private win: BrowserWindow;

  /**
   * WebSocket
   */
  private ws: WebSocket | null = null;
  private url: string;
  private isReady = false;
  private queue: Array<{
    resolve: (data: any) => void;
    reject: (err: any) => void;
  }> = [];

  constructor(url = "ws://localhost:1818", win?: BrowserWindow) {
    this.url = url;
    if (win) {
      this.win = win;
    }
  }

  /**
   * 连接 WebSocket
   */
  private connect(): Promise<void> {
    if (this.ws && this.isReady) return Promise.resolve();
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.url);
      this.ws.onopen = () => {
        this.isReady = true;
        resolve();
      };
      this.ws.onerror = err => {
        this.isReady = false;
        reject(err);
      };
      this.ws.onclose = () => {
        this.isReady = false;
      };
      this.ws.onmessage = event => {
        const data = event.data as OCXMessageType;

        try {
          if (data.startsWith("Begin")) {
            // TODO: 解析请求
          } else {
            // 图片流
            this.handleImageStream(event.data);
          }
        } catch (e) {
          // todo: 解析失败
        }
      };
    });
  }

  /**
   * 断开 WebSocket
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.isReady = false;
    }
  }

  /**
   * 发送请求
   */
  private async sendRequest<T = any>(req: OCXCommand): Promise<T> {
    await this.connect();
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN)
      throw new Error("WebSocket not connected");
    return new Promise((resolve, reject) => {
      this.queue.push({ resolve, reject });

      // 拼接参数
      const params = Object.entries(req);

      this.ws?.send(
        `${req.type}(${params
          .slice(1)
          .map(item => item[1])
          .join(",")})`
      );
      // 超时处理
      setTimeout(() => {
        if (this.queue.length > 0) {
          const idx = this.queue.findIndex(item => item.resolve === resolve);
          if (idx !== -1) {
            this.queue.splice(idx, 1);
            reject("Timeout");
          }
        }
      }, 5000);
    });
  }

  /**
   * 处理图片流
   * @param data 图片流
   */
  private handleImageStream(data: string) {
    if (this.win) {
      this.win.webContents.send(IPC_CHANNELS.OCX.GET_IMAGE_STREAM, {
        base64: true,
        data,
      });
    }
  }

  /**
   * 开始拍摄
   */
  startPlay() {
    this.sendRequest({
      type: "bStartPlay",
    });
  }

  /**
   * 停止拍摄
   */
  stopPlay() {
    this.sendRequest({ type: "bStopPlay" });
  }

  /**
   * 设置拍照模式
   */
  setMode(mode: OCXMode) {
    this.sendRequest({ type: "bSetMode", mode });
  }

  /**
   * 设置视频显示模式
   */
  setVideoDispMode(mode: OCXVideoDispMode) {
    this.sendRequest({ type: "vSetVideoDispMode", mode });
  }

  /**
   * 拍照
   */
  takePhoto(filePath: string, filename: string) {
    this.sendRequest({ type: "bSaveJPG", filePath, filename });
  }
}
export default DocumentCameraService;
