import { TriangleAlert } from "lucide-react";
import { motion } from "motion/react";

interface ErrorBoundaryProps {
  error: string | null;
  loadPDF: () => void;
}

const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({ error, loadPDF }) => {
  return (
    <div className="w-full h-full flex items-center justify-center rounded-lg">
      <div className="flex flex-col items-center justify-center gap-2 text-red-600">
        <TriangleAlert />
        <h3 className="text-lg font-semibold text-red-700">
          文档加载失败，请重试
        </h3>
        <p className="text-red-600">{error}</p>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={loadPDF}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          重试
        </motion.button>
      </div>
    </div>
  );
};

export default ErrorBoundary;
