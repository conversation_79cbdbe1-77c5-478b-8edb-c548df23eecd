import HttpRequestManager, { type RequestConfig, type RequestManager } from ".";

type RequestClient = Pick<
  RequestManager,
  "get" | "post" | "put" | "delete" | "request"
>;

const createDomainRequest = (config?: RequestConfig): RequestClient => {
  const manager = HttpRequestManager.getInstance();
  const name =
    config?.instanceName ||
    `domain_${config?.baseURL?.replace(/[^a-zA-Z0-9]/g, "_")}`;
  manager.createInstance(name, { ...config });

  return {
    get: (url, config) => manager.get(url, { ...config, instanceName: name }),

    post: (url, data, config) =>
      manager.post(url, data, { ...config, instanceName: name }),

    put: (url, data, config) =>
      manager.put(url, data, { ...config, instanceName: name }),

    delete: (url, config) =>
      manager.delete(url, { ...config, instanceName: name }),

    request: (config: any) =>
      manager.request({ ...config, instanceName: name }),
  };
};

/**
 * 默认请求
 */
const httpRequest = createDomainRequest({
  baseURL: import.meta.env.VITE_API_URL,
});

/**
 * 百度OCR
 */
const baiduOCR = createDomainRequest({
  baseURL: import.meta.env.VITE_BAIDU_OCR_URL,
  instanceName: "baidu_ocr",
});

/**
 * 微信消息
 */
const wxmsgRequest = createDomainRequest({
  baseURL: import.meta.env.VITE_WXMSG_SHOWINFO_URL,
  instanceName: "wxmsg",
  timeout: 60000,
});

export { baiduOCR, httpRequest, wxmsgRequest };
