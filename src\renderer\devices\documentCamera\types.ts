export const OCXMessageResponse = [
  "BeginbStartPlaytrueEndbStartPlay", // 发送 bStartPlay 请求 首次返回
  "BeginbStartPlay2trueEndbStartPlay2", // 发送 bStartPlay2 请求 首次返回
  "BeginbSaveMergeStarttrueEndbSaveMergeStart", // 发送 bSaveMergeStart 请求 首次返回
  "BeginbSaveJPGtrueEndbSaveJPG", // 发送 bSaveJPG 请求 首次返回
  "BeginbSaveJPGExtrueEndbSaveJPGEx", // 发送 bSaveJPGEx 请求 首次返回
  "BeginBase64EncodetrueEndBase64Encode", // 发送 Base64Encode 请求 首次返回
  "BeginReadCardtrueEndReadCard", // 发送 ReadCard 请求 首次返回
  "BeginsGetBase64trueEndsGetBase64", // 发送 sGetBase64 请求 首次返回
  "BeginbSetModetrueEndbSetMode", // 发送 bSetMode 请求 首次返回
] as const;

export const OCXCommandStr = [
  "BarCodeTransfer",
  "bSaveJPGEx",
  "bSaveJPG",
  "Base64Encode",
  "ReadCard",
  "sGetBase64",
  "bStopPlay",
  "vSetResolution",
  "bFileExist",
  "bStartPlay2",
  "bStartPlay",
  "bSaveMergeStart",
  "bSetMode",
] as const;

/**
 * 高拍仪 OCX 消息类型
 * 除下面类型以外,返回的为base64字符串
 */
export type OCXMessageType = (typeof OCXMessageResponse)[number];

// 拍照模式
export enum OCXMode {
  Default = 0, // 支持鼠标框选模式（默认模式）
  FixedSize = 1, // 定义固定大小拍照模式
  FixedSizeIDCard = 2, // 定义固定大小身份证拍照模式
  AutoEdge = 3, // 自动寻边
  AutoEdgeIDCard = 4, // 自动寻边身份证拍照模式
}

// 视频显示模式
export enum OCXVideoDispMode {
  Default = 0, // 4:3 比例
  FullScreen = 1, // 自适应
}

/**
 * 高拍仪 OCX 消息类型
 */
export type OCXCommand =
  | {
      type: "bStartPlay"; // 开始拍摄
    }
  | {
      type: "bStopPlay"; // 停止拍摄
    }
  | {
      type: "bSetMode"; // 设置拍照模式
      mode: OCXMode;
    }
  | {
      type: "vSetVideoDispMode"; // 设置视频显示模式
      mode: OCXVideoDispMode;
    }
  | {
      type: "bSaveJPG"; // 保存JPG
      filePath: string; // 保存路径
      filename: string; // 保存的文件名称（不包含后缀）
    }
  | {
      type: "bSavePNG"; // 保存PNG
      filePath: string; // 保存路径
      filename: string; // 保存的文件名称（不包含后缀）
    }
  | {
      type: "bSaveTifJPG"; // 保存黑白JPG
      filePath: string; // 保存路径
      filename: string; // 保存的文件名称（不包含后缀）
    };
