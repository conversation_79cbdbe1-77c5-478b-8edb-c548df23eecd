import type {
  DisputeFormv2Detail,
  Passage,
} from "@/renderer/service/dispute/types";
import type { DisputePageType, DisputeType } from "@/types/views/dispute";

export interface ClientState {
  /**
   * 设备ID
   *
   * @description 目前暂无用户中心，默认设置为801
   * @default 801
   * @type {number}
   */
  mid: number;
  /**
   * 扫码ID
   *
   * @description 区分操作人
   * @default 54re697gre4
   * @type {string}
   */
  tid: string;
  /**
   * 分析状态
   *
   * @description 用于记录分析状态
   * @type {boolean}
   */
  isAnalyzing: boolean;
  /**
   * 当前选中的纠纷类型
   *
   * @description 用于记录用户在步骤一中选中的纠纷类型
   * @type {DisputeType}
   */
  disputeType?: DisputeType;
  /**
   * 当前选中的纠纷表单类型
   *
   * @description 用于记录用户在步骤一中选中的纠纷表单类型
   * @type {DisputePageType}
   */
  disputePageType?: DisputePageType;
  /**
   * 提取要素表的段落
   *
   * @description 用于记录用户在步骤二中选中的要素表
   */
  passage:
    | {
        [key in Passage]?: {
          /** 提取的文本 */
          text: Record<string, any> | null;
          /** 提取的状态 */
          status: "loading" | "updating" | "finished";
        };
      }
    | null;

  /**
   * 进入的方式
   *
   * - empty 空模板
   * - upload 材料上传  使用次数+1 打印次数+1
   * - scan 扫码上传    使用次数+1 打印次数+1
   * - manual 手动输入  打印次数+1
   *
   * @description 用于记录进入的方式
   * @type {"empty" | "upload" | "scan" | "manual"}
   */
  enterType: "empty" | "upload" | "scan" | "manual";

  /**
   * 当前激活的案由
   */
  activeDispute: DisputeFormv2Detail[];

  /**
   * 是否处于打印状态
   */
  isPrint: boolean;
}
