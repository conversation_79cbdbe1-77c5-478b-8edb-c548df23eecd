import FilePreview from "@/renderer/components/FilePreview";
import FileDrag from "@/renderer/components/StepTwo/FileDrag";
import StepFileBar from "@/renderer/components/StepTwo/StepFileBar";
import StepTools from "@/renderer/components/StepTwo/StepTools";
import { useDragSort } from "@/renderer/hooks";
import useFilesAction from "@/renderer/hooks/useFilesAction";
import { useAppDispatch, useAppSelector } from "@/renderer/store/hooks";
import { updateUploadType } from "@/renderer/store/slice/file";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import UsbInsertTips from "./InsertCenter";

const FileArea: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { files } = useDragSort();
  const { uploadType, clearFolder } = useFilesAction();

  const handlePrev = () => {
    navigate(-1);
    clearFolder();
  };

  /**
   * 关闭预览
   */
  const [previewIndex, setPreviewIndex] = useState(-1);
  const handleClosePreview = () => {
    setPreviewIndex(-1);
  };

  const client = useAppSelector(state => state.client);

  /** 开始分析 */
  const onNext = async () => {
    const { mid, disputePageType, disputeType } = client || {};
    if (!mid || !disputePageType || !disputeType) return;
    navigate("/dispute/step-three");
  };

  if (uploadType === "scan") return null;

  return (
    <>
      <FilePreview
        files={files}
        index={previewIndex}
        open={previewIndex !== -1}
        onClose={handleClosePreview}
        pdfClassName="h-[45.78vw]"
      />

      <div className="relative flex flex-col flex-1 gap-[1.6vw] overflow-hidden pb-[.83vw] ">
        <StepFileBar
          type={uploadType}
          className="mb-[1.6vw]"
          onClick={() =>
            dispatch(
              updateUploadType(uploadType === "camera" ? "usb" : "camera")
            )
          }
        />

        {files.length === 0 && <UsbInsertTips />}

        {/* 文件 */}
        {files.length > 0 && (
          <FileDrag onPreview={(_, index) => setPreviewIndex(index)} />
        )}

        {/* 上一步和开始分析按钮 */}
        <div className="flex justify-between items-center mt-auto">
          <StepTools
            className="relative bottom-0 right-0 justify-between flex w-full mr-[1.5vw]"
            onPrev={handlePrev}
            onNext={onNext}
            count={files.length}
          />
        </div>
      </div>
    </>
  );
};

export default FileArea;
