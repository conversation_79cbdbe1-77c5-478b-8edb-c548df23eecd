import bg from "@assets/step-one/bt1.png";
import activeBg from "@assets/step-one/bt2.png";
import type React from "react";

type Props = {
  children: React.ReactNode;
  onClick: () => void;
  active?: boolean;
};

const Button: React.FC<Props> = ({ children, onClick, active = false }) => {
  return (
    <div
      className="w-[9.115vw] h-[3.5vw] text-white text-[1.25vw] flex justify-center leading-none pt-[0.83vw]"
      style={{
        backgroundImage: `url(${active ? activeBg : bg})`,
        backgroundSize: "100% 100%",
      }}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

export default Button;
