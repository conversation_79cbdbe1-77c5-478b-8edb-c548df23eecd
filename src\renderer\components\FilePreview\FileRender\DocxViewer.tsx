import { renderAsync } from "docx-preview";
import React, { useEffect, useRef } from "react";
import { twMerge } from "tailwind-merge";

interface DocxViewerProps {
  filePath: string | Blob | ArrayBuffer;
  fileName?: string;
  className?: string;
  style?: React.CSSProperties;
}

const DocxViewer: React.FC<DocxViewerProps> = ({
  filePath,
  className,
  style,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!filePath || !container) return;

    const loadDocx = async () => {
      let arrayBuffer: ArrayBuffer;

      try {
        if (filePath instanceof ArrayBuffer) {
          arrayBuffer = filePath;
        } else if (filePath instanceof Blob) {
          arrayBuffer = await filePath.arrayBuffer();
        } else if (typeof filePath === "string") {
          if (/^https?:\/\//.test(filePath)) {
            // 远程 URL
            const response = await fetch(filePath);
            if (!response.ok) throw new Error("Failed to fetch remote docx");
            arrayBuffer = await response.arrayBuffer();
          } else if (window?.electronAPI?.usb?.readFile) {
            // 本地 Electron 路径
            const buffer = await window.electronAPI.usb.readFile(filePath);
            arrayBuffer = new ArrayBuffer(buffer.length);
            new Uint8Array(arrayBuffer).set(buffer);
          } else {
            throw new Error("Unsupported file path");
          }
        } else {
          throw new Error("Unsupported file type");
        }

        // 清空旧内容
        container.innerHTML = "";

        // 渲染 DOCX 内容
        await renderAsync(arrayBuffer, container, undefined, {
          inWrapper: true,
          className: "docx",
          ignoreWidth: true,
          ignoreHeight: true,
          ignoreFonts: true,
          breakPages: false,
          useBase64URL: false,
          experimental: false,
        });
      } catch (err) {
        console.error("Failed to load DOCX:", err);
        container.innerHTML = `<div style="color: red;">加载文档失败</div>`;
      }
    };

    loadDocx();

    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = "";
      }
    };
  }, [filePath]);

  return (
    <div
      ref={containerRef}
      className={twMerge("overflow-auto", className)}
      style={{
        width: "100%",
        height: "100%",
        padding: 12,
        ...style,
      }}
    />
  );
};

export default DocxViewer;
