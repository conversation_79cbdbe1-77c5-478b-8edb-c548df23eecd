import { AnimatePresence, motion } from "motion/react";

interface ThumbnailSideProps {
  /** 是否显示 */
  visible: boolean;
  /** 总页数 */
  totalPages: number;
  /** 缩略图 */
  thumbnails: Record<number, string>;
  /** 当前页 */
  currentPage: number;
  /** 跳转页 */
  goToPage: (pageNum: number) => void;
}

const ThumbnailSide: React.FC<ThumbnailSideProps> = ({
  visible,
  totalPages,
  thumbnails,
  currentPage,
  goToPage,
}) => {
  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, x: -100 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -100 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="h-full w-48"
        >
          <motion.div className="w-48 border-r h-full  border-gray-200 default-scrollbar bg-gray-50">
            <motion.div className="flex flex-col h-full overflow-hidden">
              <h4 className="p-2 text-sm font-medium text-gray-700 mb-2">
                目录
              </h4>
              <motion.div className="space-y-2 flex-1 overflow-y-auto">
                {Array.from({ length: totalPages }, (_, index) => {
                  const pageNum = index + 1;
                  const thumbnail = thumbnails[pageNum];

                  return (
                    <div
                      key={pageNum}
                      onClick={() => goToPage(pageNum)}
                      className={`m-2 cursor-pointer p-1 rounded border-2 transition-colors ${
                        pageNum === currentPage
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      {thumbnail ? (
                        <img
                          src={thumbnail}
                          alt={`第 ${pageNum} 页`}
                          className="w-full h-auto"
                        />
                      ) : (
                        <div className="w-full h-24 bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                          加载中...
                        </div>
                      )}
                      <div className="text-xs text-center mt-1 text-gray-600">
                        第 {pageNum} 页
                      </div>
                    </div>
                  );
                })}
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ThumbnailSide;
