const env = import.meta.env;
const Domain = {
  // 文档编辑器
  docx: env.VITE_DOCX_EDITOR_URL,
  // 接口
  api: env.VITE_API_URL,
  // 百度ocr
  baiduOcr: env.VITE_BAIDU_OCR_URL,
  // 二维码
  qrcode: env.VITE_QRCODE_URL,
  // 微信消息
  wxmsg: env.VITE_WXMSG_SHOWINFO_URL,
  // 公共
  public: env.VITE_PUBLIC_URL,
  // 工具
  util: env.VITE_UTIL_URL,
};

/**
 * 获取意见反馈地址
 * @param mid 用户id
 * @returns 意见反馈地址
 */
const getFeedBackUrl = (mid: number) => {
  return `${Domain.util}/feedback/index.html?mid=${mid}`;
};

/**
 * 获取扫描地址
 * @param mid 用户id
 * @returns 扫描地址
 */
const getScanUrl = (mid: number) => {
  return `${Domain.util}/ysb/index.html?mid=${mid}`;
};

export { getFeedBackUrl, getScanUrl };

export default Domain;
