import type React from "react";

interface Props {
  /** 文件名称 */
  fileName: string;
  /** 转换耗时 */
  conversionTime?: number;
  /** LibreOffice 是否可用 */
  libreOfficeAvailable?: boolean;
  /** 错误信息 */
  error?: boolean;
  /** 重试 */
  handleRetry?: () => void;
  /** 安装 LibreOffice */
  onInstallLibreOffice: () => void;
}

const WordTabBar: React.FC<Props> = ({
  fileName,
  conversionTime,
  libreOfficeAvailable,
  error,
  handleRetry,
  onInstallLibreOffice,
}) => {
  return (
    <div className="flex items-center justify-between px-4 py-3 bg-gray-50 border-b">
      <div className="flex items-center space-x-2">
        <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
          <span className="text-white text-sm font-bold">W</span>
        </div>
        <div>
          <h3 className="text-sm font-medium text-gray-900">{fileName}</h3>
          {conversionTime && (
            <p className="text-xs text-gray-500">
              转换耗时: {conversionTime}ms
            </p>
          )}
        </div>
      </div>

      <div className="flex items-center space-x-2">
        {libreOfficeAvailable === false && (
          <button
            onClick={onInstallLibreOffice}
            className="px-3 py-1 text-xs bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
          >
            安装 LibreOffice
          </button>
        )}
        {error && (
          <button
            onClick={handleRetry}
            className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            重试
          </button>
        )}
      </div>
    </div>
  );
};

export default WordTabBar;
