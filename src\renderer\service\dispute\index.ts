import { type ResponseData } from "@/renderer/utils/request";
import { httpRequest, wxmsgRequest } from "@/renderer/utils/request/factory";
import {
  DisputeForm,
  type DisputeFormPayload,
  type DisputeFormv2,
  type DisputeFormv2Detail,
  type ExtractPayload,
  type ExtractResponse,
  type SubmitUseCountPayload,
  type UploadedFiles,
  type UploadedFilesPayload,
} from "./types";

/**
 * 获取要素表名称等数据
 * @param payload 请求参数
 * @returns 要素表名称等数据
 */
export const getDisputeForm = (payload: DisputeFormPayload) =>
  httpRequest.post<ResponseData<DisputeForm[]>>(
    "/Trust/Transfer/ele_get_info",
    payload
  );

/**
 * 获取要素表名称等数据
 * @param payload 请求参数
 * @returns 要素表名称等数据
 */
export const getDisputeFormv2 = (payload: DisputeFormPayload) =>
  httpRequest.post<ResponseData<DisputeFormv2>>(
    "/Trust/Transfer/ele_package_anyou",
    payload
  );

/**
 * 获取案由详情(单个)
 *
 * @param payload 请求参数
 * @param requestId 请求id
 * @returns 要素表名称等数据
 */
export const getDisputeFormv2DetailOne = (payload: { anyou_id: number }) =>
  httpRequest.post<ResponseData<DisputeFormv2Detail>>(
    "/Trust/Transfer/anyou_detail_one",
    payload
  );

/**
 * 获取案由详情
 *
 * @param payload 请求参数
 * @param requestId 请求id
 * @returns 要素表名称等数据
 */
export const getDisputeFormv2Detail = (payload: { anyou_id: number }) =>
  httpRequest.post<ResponseData<DisputeFormv2Detail[]>>(
    "/Trust/Transfer/anyou_detail",
    payload
  );

// 提取要素表
export const extractDisputeForm = (
  payload: ExtractPayload,
  requestId?: string
) =>
  wxmsgRequest.post<ExtractResponse>("/Element/extract", payload, {
    requestId,
  });

/**
 * 轮询获取移送端上传文件
 */
export const getUploadedFiles = (params: UploadedFilesPayload) =>
  httpRequest.get<ResponseData<UploadedFiles[]>>(
    "/Trust/Transfer/ele_get_file",
    { params }
  );

/**
 * 上传文件
 */
export const uploadFile = (
  payload: UploadedFilesPayload & { file: File[] }
) => {
  const formData = new FormData();
  payload.file.forEach(file => {
    formData.append("file", file);
  });

  formData.append("mid", payload.mid.toString());
  formData.append("tid", payload.tid.toString());

  return httpRequest.post<ResponseData<string[]>>(
    "/Trust/Transfer/ele_upload",
    payload,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
};

/**
 * 提交使用次数
 * @param params 请求参数
 * @returns
 */
export const submitUseCount = (params: SubmitUseCountPayload) =>
  httpRequest.post<ResponseData<string>>(
    "/Trust/Transfer/ele_count_info",
    params
  );
