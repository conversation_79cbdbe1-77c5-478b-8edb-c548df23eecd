import type { DisputeFormPayload } from "@/renderer/service/dispute/types";

/**
 * 纠纷表单类型枚举
 */
export enum DisputePageType {
  /** 起诉状 */
  LAWSUIT = "qsz",
  /** 答辩状 */
  RESPONSE = "dbz",
  /** 申请书 */
  APPLICATION = "sqs",
  /** 自诉状 */
  PRIVATE_PROSECUTION = "zsz",
  /** 陈述书 */
  STATEMENT = "css",
  /** 答辩意见 */
  DEFENSE_OPINION = "dbyj",
}

export type DisputeFormV2Payload = {
  append?: boolean;
  force: boolean;
} & DisputeFormPayload;

/**
 * 纠纷类型枚举
 */
export enum DisputeType {
  /** 买卖合同纠纷 */
  SALE_CONTRACT = "mmht",
  /** 保证保险合同纠纷 */
  GUARANTEE_INSURANCE = "bzbxht",
  /** 信用卡纠纷 */
  CREDIT_CARD = "yhxyk",
  /** 劳动争议纠纷 */
  LABOR_DISPUTE = "ldzy",
  /** 机动车交通事故责任纠纷 */
  TRAFFIC_ACCIDENT = "jdcjtsgzr",
  /** 民间借贷纠纷 */
  PRIVATE_LENDING = "mjjd",
  /** 物业服务合同纠纷 */
  PROPERTY_SERVICE = "wyfwht",
  /** 离婚纠纷 */
  DIVORCE = "lh",
  /** 融资租赁合同纠纷 */
  FINANCIAL_LEASE = "rzzl",
  /** 证券虚假陈述责任纠纷 */
  SECURITIES_FRAUD = "zqxjcszr",
  /** 金融借款合同纠纷 */
  FINANCIAL_LOAN = "jrjkht",
}
