import axios, {
  AxiosError,
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";
import {
  ERROR_MESSAGES,
  RETRY_DELAY,
  RETRY_STATUS_CODES,
  STATUS_CODE,
} from "./config";
import { ErrorResponse, RequestConfig, ResponseData } from "./types";

// 扩展Window接口以支持electron
declare global {
  interface Window {
    electron?: {
      showNotification?: (message: string) => void;
    };
  }
}

/**
 * Token管理器
 */
class TokenManager {
  private static instance: TokenManager;
  private token: string | null = null;

  public static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * 设置token
   */
  public setToken(token: string): void {
    this.token = token;
    // 将token存储到localStorage
    localStorage.setItem("auth_token", token);
  }

  /**
   * 获取token
   */
  public getToken(): string | null {
    if (!this.token) {
      // 从localStorage获取token
      this.token = localStorage.getItem("auth_token");
    }
    return this.token;
  }

  /**
   * 清除token
   */
  public clearToken(): void {
    this.token = null;
    localStorage.removeItem("auth_token");
  }
}

/**
 * 错误处理器
 */
class ErrorHandler {
  /**
   * 处理HTTP错误
   */
  public static handleHttpError(error: AxiosError): ErrorResponse {
    const { response, request, message } = error;

    // 请求已发出，但服务器响应了错误状态码
    if (response) {
      const { status, data } = response;

      switch (status) {
        case STATUS_CODE.UNAUTHORIZED:
          // 清除token，跳转到登录页
          TokenManager.getInstance().clearToken();
          return {
            code: status,
            msg: ERROR_MESSAGES.UNAUTHORIZED,
            details: data,
          };
        default:
          return {
            code: status,
            msg: (data as any)?.message || ERROR_MESSAGES.UNKNOWN_ERROR,
            details: data,
          };
      }
    }

    // 请求已发出，但没有收到响应
    if (request) {
      if (message.includes("timeout")) {
        return {
          code: 0,
          msg: ERROR_MESSAGES.TIMEOUT_ERROR,
        };
      }
      return {
        code: 0,
        msg: ERROR_MESSAGES.NETWORK_ERROR,
      };
    }

    // 请求配置错误
    return {
      code: 0,
      msg: message || ERROR_MESSAGES.UNKNOWN_ERROR,
    };
  }

  /**
   * 显示错误提示
   */
  public static showErrorMessage(error: ErrorResponse): void {
    console.error("Request Error:", error);

    // Electron环境，使用系统通知
    if (window.electron?.showNotification) {
      window.electron.showNotification(error.msg);
    }
  }
}

/**
 * 请求拦截器
 */
export function setupRequestInterceptor(instance: AxiosInstance): void {
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      const customConfig = config as RequestConfig & InternalAxiosRequestConfig;

      // 添加token
      if (customConfig.needAuth !== false) {
        const token = TokenManager.getInstance().getToken();
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      }

      // 显示加载状态
      if (customConfig.showLoading) {
        // 这里可以触发loading状态
        // 可以通过Redux dispatch或者其他状态管理方式
        console.log("Request started:", config.url);
      }

      return config;
    },
    (error: AxiosError) => {
      console.error("Request interceptor error:", error);
      return Promise.reject(error);
    }
  );
}

/**
 * 响应拦截器
 */
export function setupResponseInterceptor(instance: AxiosInstance): void {
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      const customConfig = response.config as RequestConfig &
        InternalAxiosRequestConfig;

      // 隐藏加载状态
      if (customConfig.showLoading) {
        console.log("Request completed:", response.config.url);
      }

      // 处理响应数据
      const responseData = response.data as ResponseData;

      // 检查业务状态码
      if (
        responseData.code !== undefined &&
        responseData.code !== STATUS_CODE.SUCCESS
      ) {
        const error: ErrorResponse = {
          code: responseData.code,
          msg: responseData.msg || ERROR_MESSAGES.UNKNOWN_ERROR,
          details: responseData,
        };

        if (customConfig.showError) {
          ErrorHandler.showErrorMessage(error);
        }

        return Promise.reject(error);
      }

      return response.data;
    },
    async (error: AxiosError) => {
      const customConfig = error.config as RequestConfig &
        InternalAxiosRequestConfig;

      // 隐藏加载状态
      if (customConfig?.showLoading) {
        console.log("Request failed:", error.config?.url);
      }

      // 处理取消请求
      if (axios.isCancel(error)) {
        console.log("Request canceled:", error.message);
        return Promise.reject({
          code: 0,
          message: ERROR_MESSAGES.CANCEL_REQUEST,
        });
      }

      // 处理重试逻辑
      if (customConfig && shouldRetry(error, customConfig)) {
        return retryRequest(instance, customConfig);
      }

      // 处理错误
      const errorResponse = ErrorHandler.handleHttpError(error);

      if (customConfig?.showError) {
        ErrorHandler.showErrorMessage(errorResponse);
      }

      return Promise.reject(errorResponse);
    }
  );
}

/**
 * 判断是否需要重试
 */
function shouldRetry(error: AxiosError, config: RequestConfig): boolean {
  // 检查是否达到重试次数
  if (!config.retryCount || config.retryCount <= 0) {
    return false;
  }

  // 检查是否是可重试的错误
  if (error.response) {
    const status = error.response.status;
    return RETRY_STATUS_CODES.some(code => code === status);
  }

  // 检查是否是网络错误
  if (!error.response && error.code !== "ECONNABORTED") {
    return true;
  }

  return false;
}

/**
 * 重试请求
 */
async function retryRequest(
  instance: AxiosInstance,
  config: RequestConfig & InternalAxiosRequestConfig
): Promise<any> {
  // 减少重试次数
  config.retryCount = (config.retryCount || 0) - 1;

  // 延迟重试
  await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));

  console.log(
    `Retrying request: ${config.url} (${config.retryCount} attempts left)`
  );

  return instance.request(config);
}
