import { useAllPagesPDFViewer } from "../hooks/useNormalPDF";
import PDFSkeleton from "./PDFSkeleton";

interface AllModeProps {
  /** 文件路径 */
  filePath: string;
  /** 初始缩放比例 */
  initialScale?: number;
  /** 自定义样式 */
  skeletonClassName?: string;
}

const AllMode: React.FC<AllModeProps> = ({
  filePath,
  initialScale = 1.0,
  skeletonClassName,
}) => {
  const { scale, pages } = useAllPagesPDFViewer(filePath, initialScale);

  return (
    <div className="flex-1 flex flex-col gap-4 overflow-hidden overflow-y-auto p-4 bg-gray-100">
      {pages.map((page, index) => {
        return (
          <div key={index} className="flex justify-center h-full flex-shrink-0">
            {page ? (
              <img
                src={page}
                alt={`第 ${index} 页`}
                className="shadow-lg bg-white"
                style={{
                  maxWidth: "100%",
                  height: "auto",
                  transform: `scale(${scale})`,
                  transformOrigin: "top center",
                }}
              />
            ) : (
              <PDFSkeleton scale={scale} className={skeletonClassName} />
            )}
          </div>
        );
      })}
    </div>
  );
};

export default AllMode;
