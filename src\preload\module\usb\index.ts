import { IPC_CHANNELS } from "@/shared/constants";
import { ipc<PERSON><PERSON><PERSON> } from "electron";
import type { Usb<PERSON><PERSON> } from "./types";

const usbAPI: UsbAPI = {
  onUsbRenderReady: () => ipcRenderer.invoke(IPC_CHANNELS.USB.USB_RENDER_READY),

  onUsbChange: callback =>
    ipcRenderer.on(IPC_CHANNELS.USB.USB_CHANGE, (_, data) => callback(data)),

  readFiles: () => ipcRenderer.invoke(IPC_CHANNELS.USB.READ_FILES),

  selectPath: () => ipcRenderer.invoke(IPC_CHANNELS.USB.SELECT_PATH),

  setPath: path => ipcRenderer.invoke(IPC_CHANNELS.USB.SET_PATH, path),

  readFile: filePath =>
    ipcRenderer.invoke(IPC_CHANNELS.USB.READ_FILE, filePath),

  readPdfFile: filePath =>
    ipc<PERSON>ender<PERSON>.invoke(IPC_CHANNELS.USB.READ_PDF_FILE, filePath),

  readDocxFile: filePath =>
    ipcRenderer.invoke(IPC_CHANNELS.USB.READ_DOCX_FILE, filePath),

  getFileInfo: filePath =>
    ipcRenderer.invoke(IPC_CHANNELS.USB.GET_FILE_INFO, filePath),

  toggleFileSelection: (fileId, selected) =>
    ipcRenderer.invoke(
      IPC_CHANNELS.USB.TOGGLE_FILE_SELECTION,
      fileId,
      selected
    ),

  getSelectedFiles: () =>
    ipcRenderer.invoke(IPC_CHANNELS.USB.GET_SELECTED_FILES),

  clearSelection: () => ipcRenderer.invoke(IPC_CHANNELS.USB.CLEAR_SELECTION),
};

export default usbAPI;
