import React from "react";
import { FileRenderProps } from "../types";

interface ToolBarProps extends FileRenderProps {
  /** 显示模式 */
  displayMode: "single" | "all";
  /** 页面变化 */
  onPageChange: (page: number) => void;
  /** 当前页 */
  currentPage: number;
  /** 总页数 */
  totalPages: number;
}

const ToolBar: React.FC<ToolBarProps> = ({
  displayMode,
  currentPage,
  onPageChange,
  totalPages,
}) => {
  return (
    <div className="flex items-center justify-between p-3 border-b bg-gray-50">
      <div className="flex items-center space-x-2">
        {/* 页面导航（仅在单页模式显示） */}
        {displayMode === "single" && (
          <>
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage <= 1}
              className="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              title="上一页 (←)"
            >
              ←
            </button>

            <span className="text-sm text-gray-600">
              {currentPage} / {totalPages}
            </span>

            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
              className="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              title="下一页 (→)"
            >
              →
            </button>

            {/* 页面跳转 */}
            <input
              type="number"
              min={1}
              max={totalPages}
              value={currentPage}
              onChange={e => onPageChange(parseInt(e.target.value) || 1)}
              className="w-16 px-2 py-1 text-sm border border-gray-300 rounded"
              title="输入页码"
            />
          </>
        )}
      </div>
    </div>
  );
};

export default ToolBar;
