import nextStep from "@assets/step-two/next-step.png";
import prevStep from "@assets/step-two/prev-step.png";
import { motion } from "motion/react";
import { twMerge } from "tailwind-merge";

interface Props {
  count?: number;
  onPrev: () => void;
  onNext: () => void;
  className?: string;
}

const StepTools: React.FC<Props> = ({ count, onPrev, onNext, className }) => {
  const classes = twMerge(
    "absolute flex gap-[1.46vw] right-[1.35vw] bottom-[.73vw]",
    className
  );

  return (
    <div className={classes}>
      <motion.img
        src={prevStep}
        alt=""
        className="w-[9.53vw] h-[3.23vw] cursor-pointer"
        onClick={onPrev}
        whileTap={{ scale: 0.95 }}
      />

      {!!count && (
        <motion.div
          className="min-w-[9.53vw] h-[3.23vw] text-[1.35vw] text-white flex items-center justify-center cursor-pointer"
          style={{
            background: `url(${nextStep}) no-repeat center center`,
            backgroundSize: "100% 100%",
            backgroundPosition: "center",
          }}
          onClick={onNext}
          whileTap={{ scale: 0.95 }}
        >
          （{count}）开始分析
        </motion.div>
      )}
    </div>
  );
};

export default StepTools;
