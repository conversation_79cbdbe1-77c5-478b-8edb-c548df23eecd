import { secretMd5 } from "@/renderer/config/app";
import { extractDisputeForm } from "@/renderer/service/dispute";
import type {
  ExtractResponse,
  Passage,
} from "@/renderer/service/dispute/types";
import { useAppDispatch, useAppSelector } from "@/renderer/store/hooks";
import {
  startFetchingPassage,
  updatePassage,
} from "@/renderer/store/slice/client";
import { combineFileText, hasLoading } from "@/renderer/store/slice/file";
import HttpRequestManager from "@/renderer/utils/request";
import { useCallback, useEffect, useRef } from "react";

/**
 * 成功解析文件文字后，执行提取要素表的段落
 * 图片类型OCR 未识别成功，需等待
 */
const useAnalysis = () => {
  const dispatch = useAppDispatch();
  /** 请求ID队列 */
  const requestIdQueue = useRef<Set<string>>(new Set());
  /** 文件文本 */
  const fileText = useAppSelector(combineFileText);
  /** 是否存在正在加载的文件 */
  const isLoading = useAppSelector(hasLoading);

  const mid = useAppSelector(state => state.client?.mid);
  const disputePageType = useAppSelector(
    state => state.client?.disputePageType
  );
  const disputeType = useAppSelector(state => state.client?.disputeType);
  const isAnalyzing = useAppSelector(state => state.client?.isAnalyzing);

  /** 开始分析 */
  const precessAnalysis = useCallback(async () => {
    if (!mid || !disputePageType || !disputeType) return;

    const { timestamp, signature, appid } = secretMd5();
    const text = fileText?.map(item => item.text).join("");
    if (!text) return;

    const passage = [
      "dsrxx",
      "dsrxx_1",
      "ssqqhyj",
      "ssqqhyj_1",
      "sshly",
      "sshly_1",
    ] as Passage[];

    // 开始提取要素表的段落
    dispatch(startFetchingPassage(passage));

    // 提取要素表的段落
    for (const type of passage) {
      const requestId = `extract-${type}`;
      requestIdQueue.current.add(requestId);

      extractDisputeForm(
        {
          query_text: text,
          appid,
          timestamp,
          signature,
          mid,
          doc_type: disputePageType,
          passage: type,
          type: disputeType,
          stream: false,
        },
        requestId
      )
        .then(res => {
          let text = {};
          if (typeof res === "string") {
            const str = (res as string).slice(0, -2);
            text = JSON.parse(
              (JSON.parse(str) as ExtractResponse).choices[0].message.content
            );
          } else {
            text = JSON.parse(res.choices[0].message.content);
          }

          dispatch(
            updatePassage({
              [type]: { text, status: "finished" },
            })
          );
        })
        .finally(() => {
          requestIdQueue.current.delete(`extract-${type}`);
        });
    }
  }, [dispatch, disputePageType, disputeType, fileText, mid]);

  /** 取消请求 */
  const cancelRequest = useCallback(() => {
    requestIdQueue.current.forEach(item => {
      HttpRequestManager.getInstance().cancelRequest(item);
    });
  }, []);

  useEffect(() => {
    if (!isLoading && isAnalyzing) {
      precessAnalysis();
    }

    return () => {
      cancelRequest();
    };
  }, [isLoading, isAnalyzing, precessAnalysis, cancelRequest]);
};

export default useAnalysis;
