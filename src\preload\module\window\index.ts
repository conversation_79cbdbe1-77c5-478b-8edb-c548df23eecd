import { IPC_CHANNELS } from "@/shared/constants";
import { ipc<PERSON><PERSON><PERSON> } from "electron";
import type { WindowAPI } from "./types";

const windowAPI: WindowAPI = {
  isMaximized: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW.IS_MAXIMIZED),
  isMinimized: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW.IS_MINIMIZED),
  minimize: () => ipcRenderer.send(IPC_CHANNELS.WINDOW.MINIMIZE),
  maximize: () => ipcRenderer.send(IPC_CHANNELS.WINDOW.MAXIMIZE),
  restore: () => ipcRenderer.send(IPC_CHANNELS.WINDOW.RESTORE),
  close: () => ipcRenderer.send(IPC_CHANNELS.WINDOW.CLOSE),
};

export default windowAPI;
