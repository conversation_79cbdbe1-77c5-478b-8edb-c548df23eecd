@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply select-none;
  }
  @font-face {
    font-family: "Source Han Sans SC";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(./assets/fonts/SC/Source_Han_Sans_SC_Normal_Normal.otf)
      format("opentype");
  }
  @font-face {
    font-family: "Source Han Sans SC";
    font-style: bold;
    font-weight: 700;
    font-display: swap;
    src: url("./assets/fonts/SC/Source_Han_Sans_SC_Bold.otf") format("opentype");
  }
  @font-face {
    font-family: "Source Han Sans SC";
    font-style: medium;
    font-weight: 500;
    font-display: swap;
    src: url("./assets/fonts/SC/Source_Han_Sans_SC_Medium_Medium.otf")
      format("opentype");
  }
  @font-face {
    font-family: "ZQKLKT-Bold";
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url("./assets/fonts/ZQK/ZHENGQINGKELENGKUTI-2.TTF") format("truetype");
  }
}

body {
  font-family:
    "Source Han Sans SC",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Helvetica,
    Arial,
    sans-serif;
}

@layer components {
  .text-gradient {
    @apply text-transparent bg-clip-text bg-gradient-to-b  from-white from-0% via-white via-40% to-blue-600 to-100%;
  }

  .primary-gradient {
    @apply bg-gradient-to-b  from-white from-0% via-white via-40% to-blue-600 to-100%;
  }

  .primary-gradient-text {
    @apply text-transparent bg-clip-text bg-gradient-to-b from-[#ffffff] via-[#b6cae6] to-[#6c94cd];
  }
}

@layer utilities {
  .custom-scrollbar {
    /* 基础变量 */
    --scrollbar-width: 0.5vw;
    --scrollbar-height: 0.5vw;
    --scrollbar-track-bg: #0e2646;
    --scrollbar-thumb-bg: #2b5691;
    --scrollbar-thumb-hover: #3567a8;
    --scrollbar-radius: 0.5vw;
    --scrollbar-border: 0.15vw solid #0e2646;

    /* 强制关闭浏览器默认样式干扰 */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  /* 滚动条整体宽度 */
  .custom-scrollbar::-webkit-scrollbar {
    width: var(--scrollbar-width) !important;
    height: var(--scrollbar-height) !important;
  }

  /* 轨道样式 */
  .custom-scrollbar::-webkit-scrollbar-track {
    background-color: var(--scrollbar-track-bg) !important;
    border-radius: var(--scrollbar-radius) !important;
    margin: var(--tw-scroll-track-margin, 0) !important;
    margin-bottom: var(--tw-scroll-track-margin-bottom, 0) !important;
    margin-top: var(--tw-scroll-track-margin-top, 0) !important;
  }

  /* 滑块样式 */
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-bg) !important;
    border-radius: var(--scrollbar-radius) !important;
    border: var(--scrollbar-border) !important; /* 边框强制生效 */
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover) !important;
  }

  .scrollbar-hidden {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
  }
  .scrollbar-hidden::-webkit-scrollbar {
    display: none; /* Chrome/Safari/Webkit */
  }
}
