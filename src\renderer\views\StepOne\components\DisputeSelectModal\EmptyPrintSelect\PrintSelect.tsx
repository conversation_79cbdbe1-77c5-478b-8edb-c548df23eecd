import cardPrint from "@assets/step-one/card-print.png";
import passPrint from "@assets/step-one/pass-print.png";
import splitPrint from "@assets/step-one/split-print.png";
import { motion } from "motion/react";
import type React from "react";
import Content from "../Content";

type Props = {
  onCardPrint: () => void;
  onPassPrint: () => void;
};

/**
 * 打印方式选择
 */
const PrintSelect: React.FC<Props> = ({ onCardPrint, onPassPrint }) => {
  return (
    <Content>
      <motion.div
        className="w-[14vw] h-[19vw] cursor-pointer"
        onClick={onCardPrint}
        initial={{ opacity: 0, x: -100 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
        whileTap={{ scale: 0.9 }}
      >
        <img
          src={cardPrint}
          alt="cardPrint"
          className="w-full h-full"
          draggable={false}
        />
      </motion.div>

      <div className="w-[1vw] h-[22vw]">
        <img src={splitPrint} alt="splitPrint" className="w-full h-full" />
      </div>

      <motion.div
        className="w-[14vw] h-[19vw] cursor-pointer"
        onClick={onPassPrint}
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
        whileTap={{ scale: 0.9 }}
      >
        <img
          src={passPrint}
          alt="passPrint"
          className="w-full h-full"
          draggable={false}
        />
      </motion.div>
    </Content>
  );
};

export default PrintSelect;
