import { AnimatePresence, motion } from "motion/react";
import React, { useState } from "react";
import { twMerge } from "tailwind-merge";

interface CheckProps {
  containerClassName?: string;
  className?: string;
  value?: boolean;
  children?: React.ReactNode;
  onChange?: (checked: boolean) => void;
}

const CheckBox: React.FC<CheckProps> = ({
  containerClassName,
  className,
  value: controlledValue,
  onChange,
  children,
}) => {
  /**
   * 内部选中状态
   */
  const [internalChecked, setInternalChecked] = useState(false);

  /**
   * 是否选中
   */
  const isChecked =
    controlledValue !== undefined ? controlledValue : internalChecked;

  /**
   * 点击事件
   */
  const handleClick = () => {
    const newChecked = !isChecked;
    if (controlledValue !== undefined) {
      onChange?.(newChecked);
    } else {
      setInternalChecked(newChecked);
    }
  };

  return (
    <div
      className={twMerge(
        "flex items-center gap-[10px] text-white",
        containerClassName
      )}
    >
      <motion.div
        className={twMerge(
          "border border-primary-50 w-[1.56vw] h-[1.56vw] rounded-full flex items-center justify-center cursor-pointer",
          isChecked && "border-brightSky-default bg-check",
          className
        )}
        onClick={handleClick}
        transition={{ duration: 0.2 }}
      >
        <AnimatePresence>
          {isChecked && (
            <motion.svg
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 1 }}
              exit={{ pathLength: 0, opacity: 0 }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
              }}
            >
              <motion.path
                d="M5 12L10 17L19 8"
                stroke="#50b7ff"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                exit={{ pathLength: 0 }}
                transition={{
                  duration: 0.3,
                  ease: "easeInOut",
                }}
              />
            </motion.svg>
          )}
        </AnimatePresence>
      </motion.div>
      {children}
    </div>
  );
};

export default CheckBox;
