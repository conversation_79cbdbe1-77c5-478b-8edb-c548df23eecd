import { X } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import type React from "react";
import { BlueButton } from "./Button";

interface CompleteFillTipsProps {
  /**
   * 是否显示
   */
  visible?: boolean;
  /**
   * 关闭
   */
  onClose?: () => void;
  /**
   * 完成
   */
  onComplete?: () => void;
}

const CompleteFillTips: React.FC<CompleteFillTipsProps> = ({
  visible,
  onClose,
  onComplete,
}) => {
  return (
    <AnimatePresence>
      {visible && (
        <div className="fixed top-0 left-0 w-full h-full bg-transparent flex justify-center items-center">
          <motion.div
            className="bg-mask/90 w-[42vw] h-[25vw] flex flex-col  rounded-xl "
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              className="ml-auto mt-[1vw] mr-[1vw] w-[3vw] h-[3vw] rounded-full bg-white/10 flex justify-center items-center cursor-pointer"
              whileTap={{ scale: 0.9 }}
              whileHover={{ scale: 0.9 }}
              onClick={onClose}
            >
              <X className="w-[2vw] h-[2vw]" color="white" />
            </motion.div>

            <div className="w-full flex-1 flex flex-col items-center mt-[4vw]">
              <h1 className="text-[3vw] font-bold font-ZQKLKT-Bold text-gradient">
                请完整填写您的表格信息
              </h1>
              <p className="text-white text-[1vw] mb-[4vw]">
                检测到您表格中有重要信息为未填写，请进行完善。
              </p>
              <BlueButton onClick={onComplete}>去完善</BlueButton>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default CompleteFillTips;
