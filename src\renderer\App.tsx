import React from "react";
import { Provider } from "react-redux";
import { RouterProvider } from "react-router-dom";
import { PersistGate } from "redux-persist/lib/integration/react";
import DataPreloader from "./components/DataPreloader";
import LibreOfficeStatusBanner from "./components/LibreOffice/LibreOfficeStatusBanner";
import router from "./router";
import { persistor, store } from "./store";

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <PersistGate persistor={persistor}>
        <DataPreloader>
          <LibreOfficeStatusBanner />
          {/* <LibreOfficeDevTools /> */}
          <RouterProvider router={router} />
        </DataPreloader>
      </PersistGate>
    </Provider>
  );
};

export default App;
