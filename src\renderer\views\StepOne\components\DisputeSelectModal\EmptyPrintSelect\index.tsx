import IdCardSensor from "@/renderer/components/IdCardSensor";
import type React from "react";
import Content from "../Content";
import PasswordEnter from "./PasswordEnter";
import PrintSelect from "./PrintSelect";

type Props = {
  printType?: "card" | "pass";
  /** 身份证打印 */
  onCardPrint: () => void;
  /** 密码打印 */
  onPassPrint: () => void;
  /** 返回 */
  onBack?: () => void;
};

const EmptyPrintSelect: React.FC<Props> = ({
  printType,
  onCardPrint,
  onPassPrint,
  onBack,
}) => {
  return (
    <>
      {!printType && (
        <PrintSelect onCardPrint={onCardPrint} onPassPrint={onPassPrint} />
      )}

      {printType === "card" && (
        <Content className="justify-center">
          <IdCardSensor onBack={onBack} />
        </Content>
      )}

      {printType === "pass" && <PasswordEnter onBackHome={onBack} />}
    </>
  );
};

export default EmptyPrintSelect;
