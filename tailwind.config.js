/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        "ZQKLKT-Bold": ["ZQKLKT-Bold", "sans-serif"],
      },
      colors: {
        primary: {
          default: "#3D7BDA",
          90: "#2490e6",
          80: "#5781b3",
          70: "#8db9fd",
          60: "#4686df",
          50: "#274c87",
          40: "#275189",
          30: "#00baff",
          20: "#0a3264",
        },
        danger: {
          default: "#cf2b2b",
        },
        deepOcean: {
          default: "#0a1b36",
          90: "#06142a",
        },
        brightSky: {
          default: "#5094ff",
          deep: "#143e79",
          light: "#0964e0",
        },
        mask: "#030e1d",
      },
      backgroundImage: {
        check: "radial-gradient(circle, rgba(6, 20, 42, 0.2) 0%, #154688 100%)",
      },
      keyframes: {
        blink: {
          "0%, 49%": { opacity: 1 },
          "50%, 100%": { opacity: 0 },
        },
      },
      animation: {
        blink: "blink 1s steps(1, start) infinite",
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      const newUtilities = {
        ".warp": {
          width: "100%",
          "padding-inline": "3vw",
        },

        ".scrollbar-mb-1vw ": {
          "--tw-scroll-track-margin-bottom": "1vw",
        },
        ".scrollbar-mt-1vw ": {
          "--tw-scroll-track-margin-top": "1vw",
        },
      };
      addUtilities(newUtilities);
    },
  ],
};
