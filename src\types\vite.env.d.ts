/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_URL: string;
  readonly VITE_BAIDU_OCR_URL: string;
  readonly VITE_WXMSG_SHOWINFO_URL: string;
  /** 公共素材地址 */
  readonly VITE_PUBLIC_URL: string;
  /** 二维码 */
  readonly VITE_QRCODE_URL: string;
  /** 工具地址 */
  readonly VITE_UTIL_URL: string;
  /** docx编辑器地址 */
  readonly VITE_DOCX_EDITOR_URL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
