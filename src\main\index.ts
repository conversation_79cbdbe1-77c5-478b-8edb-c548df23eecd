import { mainWindow } from "@/main/Frame/Main";
import LibreOfficeService from "@/main/services/libreOfficeService";
import UsbService from "@/main/services/usbService";
import { app, BrowserWindow, Menu } from "electron";
import isDev from "electron-is-dev";
import started from "electron-squirrel-startup";
import registerProtocols from "./protocols";
import LockService, { checkLock } from "./services/lockService";
import OcxService from "./services/ocxService";
import UtilsService from "./services/utilsService";

const dongle = !isDev && process.env.DONGLE_CHECK;

if (started) {
  app.quit();
}

registerProtocols();

let win: BrowserWindow | null = null;

/**
 * 初始化应用服务
 */
const initServices = () => {
  // 初始化U盘服务
  UsbService.getInstance();
  // 初始化工具服务
  UtilsService.getInstance();
  // 初始化 LibreOffice 服务
  LibreOfficeService.getInstance();
  // 初始化高拍仪服务
  OcxService.getInstance({ win });

  // 初始化锁定服务
  if (dongle) {
    LockService.getInstance({ type: 2 });
  }
};

/**
 * 创建主窗口
 */
const createWindow = (): BrowserWindow => {
  win = mainWindow.create();
  return win;
};

/**
 * 创建菜单
 */
const createMenu = () => {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: app.name,
      submenu: [
        { label: "关于", role: "about" },
        { type: "separator" },
        { label: "退出", role: "quit" },
      ],
    },
    {
      label: "开发",
      submenu: [
        { role: "reload", label: "刷新" },
        { role: "forceReload", label: "强制刷新" },
        { role: "toggleDevTools", label: "切换开发者工具" },
      ],
    },
    // ...(isDev
    //   ? ([
    //       {
    //         label: "开发",
    //         submenu: [
    //           { role: "reload", label: "刷新" },
    //           { role: "forceReload", label: "强制刷新" },
    //           { role: "toggleDevTools", label: "切换开发者工具" },
    //         ],
    //       },
    //     ] as MenuItemConstructorOptions[])
    //   : []),
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
};

/**
 * todo: 拦截鼠标快捷键前进后退
 * 应用准备就绪时创建窗口
 */
app.whenReady().then(async () => {
  // todo: 开发环境暂不开启
  // // 检查并安装依赖，未全部安装时不进入主界面
  // const ok = await checkAndInstallAllDependencies(msg => {
  //   // todo：通过 IPC 通知渲染进程显示进度弹窗
  //   console.log(msg);
  // });
  // if (!ok) {
  //   // 依赖安装失败，弹窗提示并退出
  //   // todo：弹窗提示
  //   console.error("依赖安装失败，程序即将退出");
  //   app.quit();
  //   return;
  // }

  if (dongle) {
    const ret = await checkLock(2);
    if (ret === false) {
      console.error("检测到无效的锁状态，程序即将退出");
      app.exit(0);
      process.exit(0);
    }
  }

  createWindow();
  createMenu();
  // 初始化服务，需要使用窗口实例，故在createWindow之后
  initServices();
});

/**
 * 所有窗口关闭时的处理
 * macOS 上通常保持应用运行，即使没有窗口打开
 */
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

/**
 * 应用被激活时的处理（macOS）
 * 在没有窗口打开的情况下重新创建窗口
 */
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
