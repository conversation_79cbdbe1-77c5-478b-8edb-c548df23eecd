import { motion } from "motion/react";
import { useEffect, useState } from "react";

interface Props {
  /** 是否显示 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
}

/**
 * LibreOffice 安装指南组件
 */
const LibreOfficeInstallGuide: React.FC<Props> = ({ visible, onClose }) => {
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [checking, setChecking] = useState(false);

  useEffect(() => {
    if (visible) {
      checkLibreOfficeAvailability();
    }
  }, [visible]);

  /**
   * 检查 LibreOffice 是否可用
   */
  const checkLibreOfficeAvailability = async () => {
    setChecking(true);
    try {
      const available =
        await window.electronAPI.libreOffice.checkAvailability();
      setIsAvailable(available);
    } catch (error) {
      console.error("检查 LibreOffice 失败:", error);
      setIsAvailable(false);
    } finally {
      setChecking(false);
    }
  };

  /**
   * 获取安装指南
   */
  const getInstallGuide = () => {
    const platform = navigator.platform.toLowerCase();

    if (platform.includes("win")) {
      return {
        title: "Windows 安装指南",
        steps: [
          "访问 LibreOffice 官网: https://www.libreoffice.org/",
          "点击 '下载 LibreOffice' 按钮",
          "选择 Windows 版本并下载安装包",
          "运行安装程序，按照提示完成安装",
          "安装完成后重启应用程序",
        ],
        downloadUrl: "https://www.libreoffice.org/download/download/",
      };
    } else if (platform.includes("mac")) {
      return {
        title: "macOS 安装指南",
        steps: [
          "访问 LibreOffice 官网: https://www.libreoffice.org/",
          "点击 '下载 LibreOffice' 按钮",
          "选择 macOS 版本并下载 .dmg 文件",
          "打开 .dmg 文件，将 LibreOffice 拖拽到应用程序文件夹",
          "从应用程序文件夹启动 LibreOffice 完成初始化",
          "重启应用程序",
        ],
        downloadUrl: "https://www.libreoffice.org/download/download/",
      };
    } else {
      return {
        title: "Linux 安装指南",
        steps: [
          "使用包管理器安装:",
          "Ubuntu/Debian: sudo apt install libreoffice",
          "CentOS/RHEL: sudo yum install libreoffice",
          "Fedora: sudo dnf install libreoffice",
          "或者访问官网下载安装包",
          "安装完成后重启应用程序",
        ],
        downloadUrl: "https://www.libreoffice.org/download/download/",
      };
    }
  };

  const guide = getInstallGuide();

  if (!visible) return null;

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
              <svg
                className="w-6 h-6 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                LibreOffice 安装指南
              </h2>
              <p className="text-sm text-gray-500">用于 Word 文档预览功能</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* 状态检查 */}
        <div className="p-6 border-b bg-gray-50">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">
              LibreOffice 状态:
            </span>
            {checking ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-sm text-gray-600">检查中...</span>
              </div>
            ) : isAvailable ? (
              <div className="flex items-center space-x-2 text-green-600">
                <svg
                  className="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm font-medium">已安装</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-red-600">
                <svg
                  className="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm font-medium">未安装</span>
              </div>
            )}
          </div>

          {isAvailable && (
            <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-800">
                LibreOffice 已正确安装，Word 文档预览功能可以使用。
              </p>
            </div>
          )}
        </div>

        {/* 安装指南 */}
        {!isAvailable && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {guide.title}
            </h3>

            <div className="space-y-3 mb-6">
              {guide.steps.map((step, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    {index + 1}
                  </div>
                  <p className="text-sm text-gray-700">{step}</p>
                </div>
              ))}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex items-center space-x-2 mb-2">
                <svg
                  className="w-5 h-5 text-blue-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span className="text-sm font-medium text-blue-900">提示</span>
              </div>
              <p className="text-sm text-blue-800">
                安装完成后，请重启应用程序以确保 LibreOffice 功能正常工作。
              </p>
            </div>

            <div className="mt-6 flex space-x-3">
              <a
                href={guide.downloadUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 bg-blue-500 text-white px-4 py-2 rounded-md text-center hover:bg-blue-600 transition-colors"
              >
                下载 LibreOffice
              </a>
              <button
                onClick={checkLibreOfficeAvailability}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                重新检查
              </button>
            </div>
          </div>
        )}

        {/* 底部 */}
        <div className="p-6 border-t bg-gray-50">
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 hover:text-gray-900 transition-colors"
            >
              关闭
            </button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default LibreOfficeInstallGuide;
