import { useLocation } from "react-router-dom";
import { Step } from "../Layout/components/StepBar";

/**
 * 根据当前路由路径获取对应的步骤
 * @returns 当前步骤
 */
const useStep = (): Step => {
  const location = useLocation();
  const pathname = location.pathname;

  // 根据路由路径映射到对应的步骤
  switch (pathname) {
    case "/dispute":
      return Step.One;
    case "/dispute/step-two":
      return Step.Two;
    case "/dispute/step-two-scan":
      return Step.TwoScan;
    case "/dispute/step-three":
      return Step.Three;
    case "/dispute/step-four":
      return Step.Four;
    default:
      // 默认返回第一步
      return Step.One;
  }
};

export default useStep;
