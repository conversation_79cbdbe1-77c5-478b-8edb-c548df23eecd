import { addPublicPrefix } from "@/main/utils/path";
import { getFileCover } from "@/renderer/utils/file";
import type { UsbFileInfo } from "@/types";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type React from "react";
import CardIndex from "../CardIndex";

interface Props {
  file: UsbFileInfo;
  index: number;
  /**
   * 是否显示关闭按钮
   * @param id 文件id
   */
  onDelete?: (id: string) => void;
  /**
   * 双击预览回调
   * @param file 文件信息
   */
  onPreview?: (file: UsbFileInfo, index: number) => void;
}

/**
 * 文件卡片
 * @param file 文件
 * @param index 文件索引
 */
const FileCard: React.FC<Props> = ({ file, index, onDelete, onPreview }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: file.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(file.id);
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onPreview?.(file, index);
  };
  return (
    <div
      ref={setNodeRef}
      style={style}
      className="w-[10.5vw] overflow-hidden"
      {...attributes}
    >
      <div
        className={`relative w-[10.5vw] h-[14.56vw] bg-white rounded-[.6vw] border-[.3vw] border-[#4686df] overflow-hidden cursor-grab ${isDragging ? "cursor-grabbing" : ""}`}
        {...listeners}
        onDoubleClick={handleDoubleClick}
      >
        {/* 关闭按钮 */}
        {onDelete && (
          <div
            className="absolute top-0 right-0 w-[1.67vw] h-[1.67vw] bg-danger-default rounded-es-[.3vw] flex items-center justify-center cursor-pointer "
            onClick={handleDelete}
          >
            <div className="w-[1.3vw] h-[.1vw] bg-white rounded-full rotate-45 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"></div>
            <div className="w-[1.3vw] h-[.1vw] bg-white rounded-full -rotate-45 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"></div>
          </div>
        )}

        <img
          src={getFileCover(
            file.type,
            addPublicPrefix(file.path, {
              remote: file.remote,
            })
          )}
          alt={file.name}
          className="h-full object-contain p-3"
          draggable={false}
        />

        {/* 序号 */}
        <CardIndex index={index + 1} />
      </div>
      <div className="text-center text-[#769dda] truncate">{file.name}</div>
    </div>
  );
};

export default FileCard;
