import Layout from "@/renderer/Layout";
import StepFour from "@/renderer/views/StepFour";
import StepOne from "@/renderer/views/StepOne";
import StepTwo from "@/renderer/views/StepTwo";
import StepTwoScan from "@/renderer/views/StepTwoScan";
import { createHashRouter } from "react-router-dom";
import Home from "../views/Home";
import StepThree from "../views/StepThree";

const router = createHashRouter([
  {
    index: true,
    element: <Home />,
  },
  {
    path: "/dispute",
    element: <Layout />,
    children: [
      {
        path: "step-one",
        element: <StepOne />,
      },
      {
        path: "step-two",
        element: <StepTwo />,
      },
      {
        path: "step-two-scan",
        element: <StepTwoScan />,
      },
      {
        path: "step-three",
        element: <StepThree />,
      },
      {
        path: "step-four",
        element: <StepFour />,
      },
    ],
  },
]);

export default router;
