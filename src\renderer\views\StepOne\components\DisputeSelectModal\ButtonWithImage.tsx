import uploadDoc from "@assets/step-one/upload-doc.png";
import uploadEmpty from "@assets/step-one/upload-empty.png";
import uploadImg from "@assets/step-one/upload-img.png";
import uploadLocal from "@assets/step-one/upload-local.png";
import uploadManual from "@assets/step-one/upload-manual.png";
import uploadScan from "@assets/step-one/upload-scan.png";
import { motion } from "motion/react";
import type React from "react";

export type ButtonType =
  | "image"
  | "manual"
  | "usb"
  | "local"
  | "scan"
  | "empty"
  | "upload";

type Props = {
  type: ButtonType;
  onClick: () => void;
};

const getImage = (type: Props["type"]) => {
  switch (type) {
    case "empty":
      return (
        <img
          src={uploadEmpty}
          alt="upload-empty"
          className="w-[6.15vw] h-[5vw]"
          draggable={false}
        />
      );
    case "manual":
      return (
        <img
          src={uploadManual}
          alt="upload-manual"
          className="w-[4.9vw] h-[5vw]"
          draggable={false}
        />
      );
    case "image":
      return (
        <img
          src={uploadImg}
          alt="upload-img"
          className="w-[4.9vw] h-[5vw]"
          draggable={false}
        />
      );
    case "upload":
      return (
        <img
          src={uploadDoc}
          alt="upload-img"
          className="w-[4.9vw] h-[5vw]"
          draggable={false}
        />
      );
    case "local":
      return (
        <img
          src={uploadLocal}
          alt="upload-local"
          className="w-[4.95vw] h-[5vw]"
          draggable={false}
        />
      );
    case "scan":
      return (
        <img
          src={uploadScan}
          alt="upload-scan"
          className="w-[4.58vw] h-[5vw]"
          draggable={false}
        />
      );
    case "usb":
      return (
        <img
          src={uploadEmpty}
          alt="upload-usb"
          className="w-[4.58vw] h-[5vw]"
          draggable={false}
        />
      );
  }
};

const ButtonWithImage: React.FC<Props> = ({ type, onClick }) => {
  return (
    <motion.div
      className="cursor-pointer relative z-20 flex-1 flex items-center justify-center"
      onClick={onClick}
      whileTap={{ scale: 0.9 }}
    >
      {getImage(type)}
    </motion.div>
  );
};

export default ButtonWithImage;
