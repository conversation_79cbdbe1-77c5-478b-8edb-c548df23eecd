# EditorConfig is awesome: https://EditorConfig.org

# 根配置文件
root = true

# 所有文件的默认配置
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript/TypeScript 文件
[*.{js,jsx,ts,tsx}]
indent_style = space
indent_size = 2

# JSON 文件
[*.json]
indent_style = space
indent_size = 2

# CSS/SCSS/Less 文件
[*.{css,scss,less,sass}]
indent_style = space
indent_size = 2

# HTML 文件
[*.{html,htm}]
indent_style = space
indent_size = 2

# Markdown 文件
[*.md]
trim_trailing_whitespace = false

# YAML 文件
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# 配置文件
[*.{config.js,config.ts}]
indent_style = space
indent_size = 2

# 包管理器文件
[package.json]
indent_style = space
indent_size = 2

# Git 配置文件
[.gitignore]
indent_style = space
indent_size = 2 