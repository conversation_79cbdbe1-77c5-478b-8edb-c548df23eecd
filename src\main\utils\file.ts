/**
 * 获取可读的唯一文件名
 * @returns 可读的唯一文件名
 */
const getReadableUniqueFileName = (prefix = "file"): string => {
  const now = new Date();
  const pad = (n: number) => n.toString().padStart(2, "0");
  const timePart = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}_${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
  return `${prefix}_${timePart}_${Date.now()}`;
};

export { getReadableUniqueFileName };
