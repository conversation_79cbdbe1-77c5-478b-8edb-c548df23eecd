import { useCallback, useEffect, useRef, useState } from "react";
import useDeepCompareEffect from "./useDeepCompareEffect";

interface PollingState<T = any> {
  /** 轮询数据 */
  data: T | null;
  /** 轮询错误 */
  error: Error | null;
  /** 轮询次数 */
  attempts: number;
  /** 是否轮询中 */
  isPolling: boolean;
}

interface PollingConfig<T = any> {
  /** 轮询间隔 */
  interval?: number;
  /** 最大轮询次数 */
  maxAttempts?: number;
  /** 立即执行 */
  immediate?: boolean;
  /** 是否在组件卸载时停止轮询 */
  autoStop?: boolean;
  /**
   * 是否继续轮询
   * @param data 轮询数据
   * @param attempt 轮询次数
   * @returns true 继续轮询， false停止轮询
   */
  shouldContinue?: (data: T, attempt: number) => boolean;
  /** 轮询成功回调 */
  onSuccess?: (data: T, attempt: number) => void;
  /** 轮询失败回调 */
  onError?: (error: Error, attempt: number) => void;
  /** 轮询完成回调 */
  onComplete?: (data: T | null, attempts: number, reason: string) => void;
  /** 轮询数据 */
  onStart?: () => void;
  /** 轮询停止回调 */
  onStop?: () => void;
  /** 依赖 */
  deps?: React.DependencyList;
}

const usePolling = <T = any>(
  service: () => Promise<T>,
  options: PollingConfig<T>
) => {
  const {
    interval = 1000,
    maxAttempts = 10,
    immediate = true,
    autoStop = true,
    shouldContinue,
    onSuccess,
    onError,
    onComplete,
    onStart,
    onStop,
    deps = [],
  } = options;

  const [state, setState] = useState<PollingState<T>>({
    data: null,
    error: null,
    attempts: 0,
    isPolling: false,
  });

  // 定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  // 是否已停止
  const isStoppedRef = useRef(false);

  /** 轮询请求函数 */
  const requestFn = useRef(service);
  requestFn.current = service;

  // 更新状态
  const updateState = useCallback((partialState: Partial<PollingState<T>>) => {
    setState(prevState => ({
      ...prevState,
      ...partialState,
    }));
  }, []);
  const dataRef = useRef<T | null>(null);
  const attemptsRef = useRef(0);

  // 轮询请求
  const polling = useCallback(async () => {
    if (isStoppedRef.current) return;
    attemptsRef.current++;

    const attempt = attemptsRef.current;
    // 如果达到最大轮询次数，则停止轮询
    if (maxAttempts > 0 && attempt > maxAttempts) {
      updateState({
        isPolling: false,
        attempts: attempt,
      });
      onComplete?.(dataRef.current, attempt - 1, "maxAttempts");
      return;
    }

    try {
      updateState({
        attempts: attempt,
        error: null,
      });
      const result = await requestFn.current();
      dataRef.current = result;
      const continuePolling = shouldContinue
        ? shouldContinue(result, attempt)
        : true;

      if (continuePolling && !isStoppedRef.current) {
        updateState({
          data: result,
        });
        onSuccess?.(result, attempt);

        timerRef.current = setTimeout(polling, interval);
      } else {
        updateState({
          data: result,
          isPolling: false,
        });

        onSuccess?.(result, attempt);
        onComplete?.(result, attempt, "condition_met");
      }
    } catch (error) {
      const err = error as Error;
      updateState({
        attempts: attempt,
        error: err,
      });
      onError?.(err, attempt);

      if (!isStoppedRef.current) {
        timerRef.current = setTimeout(polling, interval);
      }
    }
  }, [
    maxAttempts,
    shouldContinue,
    interval,
    onSuccess,
    onError,
    onComplete,
    updateState,
  ]);

  const clear = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
      attemptsRef.current = 0;
      dataRef.current = null;
    }
  }, []);

  // 开始轮询
  const startPolling = useCallback(() => {
    clear();

    isStoppedRef.current = false;

    updateState({
      attempts: 0,
      error: null,
      isPolling: true,
    });

    onStart?.();
    polling();
  }, [clear, updateState, onStart, polling]);

  // 停止轮询
  const stopPolling = useCallback(() => {
    clear();

    isStoppedRef.current = true;

    updateState({
      isPolling: false,
    });

    onStop?.();
  }, [clear, onStop, updateState]);

  // 重新开始轮询
  const refresh = useCallback(() => {
    stopPolling();
    startPolling();
  }, [startPolling, stopPolling]);

  useEffect(() => {
    if (immediate) {
      startPolling();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [immediate]);

  useDeepCompareEffect(() => {
    if (deps.length > 0) {
      refresh();
    }
  }, [deps]);

  useEffect(() => {
    return () => {
      if (autoStop) {
        stopPolling();
      }
    };
  }, [autoStop, stopPolling]);

  return {
    ...state,
    start: startPolling,
    stop: stopPolling,
    refresh,
  };
};

export default usePolling;
