import { exec, execFile } from "child_process";
import { app } from "electron";
import fs from "fs";
import path from "path";

/**
 * 依赖软件信息
 */
interface Dependency {
  name: string;
  alias?: string;
  exe: string; // 相对 resources 路径
  registryKeys: string[]; // 常见注册表路径
}

/**
 * 需要检测和安装的依赖列表
 */
const dependencies: Dependency[] = [
  {
    name: "DoccameraOcx",
    exe: "DoccameraOcx.exe",
    registryKeys: [
      // 常见 32/64 位软件注册表路径
      "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
      "HKLM\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
    ],
  },
  // {
  //   name: "BYYJSetupOcx",
  //   alias: "WebSocked 1.00",
  //   exe: "BYYJSetupOcx.exe",
  //   registryKeys: [
  //     "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
  //     "HKLM\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
  //   ],
  // },
];

/**
 * 检查依赖是否已安装（通过注册表）
 * @param dep 依赖信息
 */
function isDependencyInstalled(dep: Dependency): Promise<boolean> {
  return new Promise(resolve => {
    let checked = 0;
    let found = false;
    dep.registryKeys.forEach(key => {
      // 查询注册表项下是否有相关软件名
      const cmd = `reg query "${key}" /s /f "${dep.alias || dep.name}"`;
      exec(cmd, (err: any, stdout: string) => {
        checked++;
        if (!err && stdout && stdout.includes(dep.name)) {
          found = true;
        }
        if (checked === dep.registryKeys.length) {
          resolve(found);
        }
      });
    });
  });
}

/**
 * 静默安装依赖，自动尝试常见参数
 * @param dep 依赖信息
 * @returns Promise<boolean> 安装是否成功
 */
function silentInstall(dep: Dependency): Promise<boolean> {
  const exePath = path.join(app.getAppPath(), "./resources", dep.exe);
  if (!fs.existsSync(exePath)) return Promise.resolve(false);
  const paramsList = ["/S", "/silent", "/quiet"];
  return new Promise(resolve => {
    let idx = 0;
    function tryInstall() {
      if (idx >= paramsList.length) return resolve(false);
      const params = [paramsList[idx]];
      execFile(exePath, params, { windowsHide: true }, err => {
        if (!err) {
          // 安装后再检测一次
          setTimeout(async () => {
            const ok = await isDependencyInstalled(dep);
            resolve(ok);
          }, 3000);
        } else {
          idx++;
          tryInstall();
        }
      });
    }
    tryInstall();
  });
}

/**
 * 检查并安装所有依赖
 * @returns Promise<boolean> 全部依赖是否已安装
 */
export async function checkAndInstallAllDependencies(
  onProgress?: (msg: string) => void
): Promise<boolean> {
  for (const dep of dependencies) {
    onProgress && onProgress(`正在检测依赖：${dep.name}`);
    const installed = await isDependencyInstalled(dep);
    if (!installed) {
      onProgress && onProgress(`未检测到 ${dep.name}，正在静默安装...`);
      const ok = await silentInstall(dep);
      if (!ok) {
        onProgress && onProgress(`${dep.name} 安装失败！`);
        return false;
      }
      onProgress && onProgress(`${dep.name} 安装成功。`);
    } else {
      onProgress && onProgress(`${dep.name} 已安装。`);
    }
  }
  return true;
}
