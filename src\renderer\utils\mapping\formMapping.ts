import { DisputePageType, DisputeType } from "@/types/views/dispute";
import bzbxhtLawsuit from "@assets/table/bzbxht-lawsuit.png";
import bzbxhtResponse from "@assets/table/bzbxht-response.png";
import jdcjtsgzrLawsuit from "@assets/table/jdcjtsgzr-lawsuit.png";
import jdcjtsgzrResponse from "@assets/table/jdcjtsgzr-response.png";
import jrjkhtLawsuit from "@assets/table/jrjkht-lawsuit.png";
import jrjkhtResponse from "@assets/table/jrjkht-response.png";
import ldzyLawsuit from "@assets/table/ldzy-lawsuit.png";
import ldzyResponse from "@assets/table/ldzy-response.png";
import lhLawsuit from "@assets/table/lh-lawsuit.png";
import lhResponse from "@assets/table/lh-response.png";
import mjjdLawsuit from "@assets/table/mjjd-lawsuit.png";
import mjjdResponse from "@assets/table/mjjd-response.png";
import mmhtLawsuit from "@assets/table/mmht-lawsuit.png";
import mmhtResponse from "@assets/table/mmht-response.png";
import rzzlLawsuit from "@assets/table/rzzl-lawsuit.png";
import rzzlResponse from "@assets/table/rzzl-response.png";
import wyfwhtLawsuit from "@assets/table/wyfwht-lawsuit.png";
import wyfwhtResponse from "@assets/table/wyfwht-response.png";
import yhxykLawsuit from "@assets/table/yhxyk-lawsuit.png";
import yhxykResponse from "@assets/table/yhxyk-response.png";
import zqxjcszrLawsuit from "@assets/table/zqxjcszr-lawsuit.png";
import zqxjcszrResponse from "@assets/table/zqxjcszr-response.png";

// 起诉状相关图片
import lawsuitActive from "@assets/book-type/lawsuit-a.png";
import lawsuit from "@assets/book-type/lawsuit.png";

// 答辩状相关图片
import responseActive from "@assets/book-type/response-a.png";
import response from "@assets/book-type/response.png";

// 申请书相关图片
import applicationActive from "@assets/book-type/application-a.png";
import application from "@assets/book-type/application.png";

// 自诉状相关图片
import privateProsecutionActive from "@assets/book-type/private-prosecution-a.png";
import privateProsecution from "@assets/book-type/private-prosecution.png";

// 陈述书相关图片
import statementActive from "@assets/book-type/statement-a.png";
import statement from "@assets/book-type/statement.png";

// 答辩意见相关图片
import defenseOpinionActive from "@assets/book-type/defense-opinion-a.png";
import defenseOpinion from "@assets/book-type/defense-opinion.png";

/**
 * 纠纷各种申请书类型图片
 */
export const disputeBookImages = {
  [DisputePageType.LAWSUIT]: {
    active: lawsuitActive,
    default: lawsuit,
  },
  [DisputePageType.RESPONSE]: {
    active: responseActive,
    default: response,
  },
  [DisputePageType.APPLICATION]: {
    active: applicationActive,
    default: application,
  },
  [DisputePageType.PRIVATE_PROSECUTION]: {
    active: privateProsecutionActive,
    default: privateProsecution,
  },
  [DisputePageType.STATEMENT]: {
    active: statementActive,
    default: statement,
  },
  [DisputePageType.DEFENSE_OPINION]: {
    active: defenseOpinionActive,
    default: defenseOpinion,
  },
};

/**
 * =========================================
 * 纠纷类型和表单类型映射
 * =========================================
 */

/**
 * 纠纷类型配置接口
 */
export interface DisputeTypeConfig {
  /** 纠纷类型枚举 */
  type: DisputeType;
  /** 中文名称 */
  name: string;
  /** 首字母缩写 */
  abbreviation: string;
  /** 表单缩略图映射 */
  formThumbnail: {
    [DisputePageType.LAWSUIT]: string;
    [DisputePageType.RESPONSE]: string;
  };
}

/**
 * 纠纷类型配置映射
 */
export const DISPUTE_TYPE_CONFIG: Record<DisputeType, DisputeTypeConfig> = {
  [DisputeType.SALE_CONTRACT]: {
    type: DisputeType.SALE_CONTRACT,
    name: "买卖合同纠纷",
    abbreviation: "MMHT",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: mmhtLawsuit,
      [DisputePageType.RESPONSE]: mmhtResponse,
    },
  },
  [DisputeType.GUARANTEE_INSURANCE]: {
    type: DisputeType.GUARANTEE_INSURANCE,
    name: "保证保险合同纠纷",
    abbreviation: "BZBHHT",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: bzbxhtLawsuit,
      [DisputePageType.RESPONSE]: bzbxhtResponse,
    },
  },
  [DisputeType.CREDIT_CARD]: {
    type: DisputeType.CREDIT_CARD,
    name: "信用卡纠纷",
    abbreviation: "YHXYK",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: yhxykLawsuit,
      [DisputePageType.RESPONSE]: yhxykResponse,
    },
  },
  [DisputeType.LABOR_DISPUTE]: {
    type: DisputeType.LABOR_DISPUTE,
    name: "劳动争议纠纷",
    abbreviation: "LDZY",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: ldzyLawsuit,
      [DisputePageType.RESPONSE]: ldzyResponse,
    },
  },
  [DisputeType.TRAFFIC_ACCIDENT]: {
    type: DisputeType.TRAFFIC_ACCIDENT,
    name: "机动车交通事故责任纠纷",
    abbreviation: "JDCJTSGZR",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: jdcjtsgzrLawsuit,
      [DisputePageType.RESPONSE]: jdcjtsgzrResponse,
    },
  },
  [DisputeType.PRIVATE_LENDING]: {
    type: DisputeType.PRIVATE_LENDING,
    name: "民间借贷纠纷",
    abbreviation: "MJJD",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: mjjdLawsuit,
      [DisputePageType.RESPONSE]: mjjdResponse,
    },
  },
  [DisputeType.PROPERTY_SERVICE]: {
    type: DisputeType.PROPERTY_SERVICE,
    name: "物业服务合同纠纷",
    abbreviation: "WYFWHT",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: wyfwhtLawsuit,
      [DisputePageType.RESPONSE]: wyfwhtResponse,
    },
  },
  [DisputeType.DIVORCE]: {
    type: DisputeType.DIVORCE,
    name: "离婚纠纷",
    abbreviation: "LH",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: lhLawsuit,
      [DisputePageType.RESPONSE]: lhResponse,
    },
  },
  [DisputeType.FINANCIAL_LEASE]: {
    type: DisputeType.FINANCIAL_LEASE,
    name: "融资租赁合同纠纷",
    abbreviation: "RZZL",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: rzzlLawsuit,
      [DisputePageType.RESPONSE]: rzzlResponse,
    },
  },
  [DisputeType.SECURITIES_FRAUD]: {
    type: DisputeType.SECURITIES_FRAUD,
    name: "证券虚假陈述责任纠纷",
    abbreviation: "ZQXJCSZR",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: zqxjcszrLawsuit,
      [DisputePageType.RESPONSE]: zqxjcszrResponse,
    },
  },
  [DisputeType.FINANCIAL_LOAN]: {
    type: DisputeType.FINANCIAL_LOAN,
    name: "金融借款合同纠纷",
    abbreviation: "JRJKHT",
    formThumbnail: {
      [DisputePageType.LAWSUIT]: jrjkhtLawsuit,
      [DisputePageType.RESPONSE]: jrjkhtResponse,
    },
  },
};

/**
 * 默认缩略图显示
 */
export const DEFAULT_THUMBNAIL = {
  [DisputePageType.LAWSUIT]: mjjdLawsuit,
  [DisputePageType.RESPONSE]: mjjdResponse,
};

/**
 * =========================================
 * 工具方法
 * =========================================
 */

/**
 * 获取所有纠纷类型列表
 * @returns 纠纷类型配置数组
 */
export const getAllDisputeTypes = (): DisputeTypeConfig[] => {
  return Object.values(DISPUTE_TYPE_CONFIG);
};

/**
 * 根据纠纷类型获取配置信息
 * @param type 纠纷类型
 * @returns 纠纷类型配置，如果不存在则返回undefined
 */
export const getDisputeTypeConfig = (
  type: DisputeType
): DisputeTypeConfig | undefined => {
  return DISPUTE_TYPE_CONFIG[type];
};

/**
 * 根据纠纷类型代码获取配置信息
 * @param typeCode 纠纷类型代码字符串
 * @returns 纠纷类型配置，如果不存在则返回undefined
 */
export const getDisputeTypeConfigByCode = (
  typeCode: string
): DisputeTypeConfig | undefined => {
  const disputeType = Object.values(DisputeType).find(
    type => type === typeCode
  );
  return disputeType ? DISPUTE_TYPE_CONFIG[disputeType] : undefined;
};

/**
 * 验证纠纷类型是否有效
 * @param type 纠纷类型
 * @returns 是否为有效的纠纷类型
 */
export const isValidDisputeType = (type: string): type is DisputeType => {
  return Object.values(DisputeType).includes(type as DisputeType);
};

/**
 * 获取表单缩略图
 * @param disputeType 纠纷类型
 * @param formType 表单类型
 * @returns 缩略图URL，如果不存在则返回空字符串
 */
export const getFormThumbnail = (
  disputeType: DisputeType,
  formType: DisputePageType
): string => {
  const config = DISPUTE_TYPE_CONFIG[disputeType];
  return formType in config?.formThumbnail
    ? config?.formThumbnail[formType]
    : DEFAULT_THUMBNAIL[formType];
};

/**
 * 根据纠纷类型名称获取纠纷类型
 * @param name 纠纷类型中文名称
 * @returns 纠纷类型，如果不存在则返回undefined
 */
export const getDisputeTypeByName = (name: string): DisputeType | undefined => {
  const config = Object.values(DISPUTE_TYPE_CONFIG).find(
    config => config.name === name
  );
  return config?.type;
};

/**
 * 根据缩写获取纠纷类型
 * @param abbreviation 纠纷类型缩写
 * @returns 纠纷类型，如果不存在则返回undefined
 */
export const getDisputeTypeByAbbreviation = (
  abbreviation: string
): DisputeType | undefined => {
  const config = Object.values(DISPUTE_TYPE_CONFIG).find(
    config => config.abbreviation === abbreviation
  );
  return config?.type;
};

/**
 * 获取纠纷类型统计信息
 * @returns 统计信息对象
 */
export const getDisputeTypeStats = () => {
  const types = getAllDisputeTypes();
  return {
    totalCount: types.length,
    types: types.map(type => ({
      code: type.type,
      name: type.name,
      abbreviation: type.abbreviation,
    })),
  };
};

/**
 * 搜索纠纷类型
 * @param keyword 搜索关键词（支持中文名称、缩写、代码）
 * @returns 匹配的纠纷类型配置数组
 */
export const searchDisputeTypes = (keyword: string): DisputeTypeConfig[] => {
  if (!keyword.trim()) {
    return getAllDisputeTypes();
  }

  const lowerKeyword = keyword.toLowerCase();
  return getAllDisputeTypes().filter(
    config =>
      config.name.toLowerCase().includes(lowerKeyword) ||
      config.abbreviation.toLowerCase().includes(lowerKeyword) ||
      config.type.toLowerCase().includes(lowerKeyword)
  );
};

/**
 * 获取表单类型的所有可能值
 * @returns 表单类型数组
 */
export const getAllFormTypes = (): DisputePageType[] => {
  return Object.values(DisputePageType);
};

/**
 * 验证表单类型是否有效
 * @param formType 表单类型
 * @returns 是否为有效的表单类型
 */
export const isValidFormType = (
  formType: string
): formType is DisputePageType => {
  return Object.values(DisputePageType).includes(formType as DisputePageType);
};

/**
 * 获取表单类型的中文名称
 * @param formType 表单类型
 * @returns 中文名称
 */
export const getFormTypeName = (formType: DisputePageType): string => {
  const formTypeNames: Record<DisputePageType, string> = {
    [DisputePageType.LAWSUIT]: "起诉状",
    [DisputePageType.RESPONSE]: "答辩状",
  };
  return formTypeNames[formType] || "";
};

/**
 * 获取表单类型的文件名
 * @param disputeType 纠纷类型
 * @param formType 表单类型
 * @returns 文件名
 */
export const getDisputeName = (
  disputeType?: DisputeType,
  formType?: DisputePageType
): string => {
  return `${disputeType && DISPUTE_TYPE_CONFIG[disputeType].name}-${formType && getFormTypeName(formType)}`;
};

/**
 * 默认导出映射配置
 */
export default DISPUTE_TYPE_CONFIG;
