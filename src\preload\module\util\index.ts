import { IPC_CHANNELS } from "@/shared/constants";
import { ipc<PERSON><PERSON><PERSON> } from "electron";
import type { Util<PERSON><PERSON> } from "./types";

const utilAPI: UtilAPI = {
  readFileAsBase64: filePath =>
    ipcRenderer.invoke(IPC_CHANNELS.UTIL.READ_FILE_AS_BASE64, filePath),

  readFileAsBuffer: filePath =>
    ipcRenderer.invoke(IPC_CHANNELS.UTIL.READ_FILE_AS_BUFFER, filePath),

  fetchJsonFromUrl: url =>
    ipcRenderer.invoke(IPC_CHANNELS.UTIL.FETCH_JSON_FROM_URL, url),

  convertWordToHtml: wordFilePath =>
    ipcRenderer.invoke(IPC_CHANNELS.UTIL.CONVERT_WORD_TO_HTML, wordFilePath),

  saveFileToTemp: (buffer, fileName) =>
    ipcRenderer.invoke(IPC_CHANNELS.UTIL.SAVE_FILE_TO_TEMP, {
      buffer,
      fileName,
    }),

  saveFileToPath: (buffer, defaultPath) =>
    ipcRenderer.invoke(IPC_CHANNELS.UTIL.SAVE_FILE_TO_PATH, {
      buffer,
      defaultPath,
    }),

  print: (html, duplex, pdfPage) =>
    ipcRenderer.invoke(IPC_CHANNELS.UTIL.PRINT, html, duplex, pdfPage),

  onPrintStatus: callback =>
    ipcRenderer.on(IPC_CHANNELS.UTIL.PRINT_STATUS, (_, data) => callback(data)),
};

export default utilAPI;
