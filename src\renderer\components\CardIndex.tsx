import { twMerge } from "tailwind-merge";

interface Props {
  className?: string;
  index: number;
}

/**
 * 卡片序号
 * @param index 序号
 */
const CardIndex: React.FC<Props> = ({ index, className }) => {
  const classes = twMerge(
    "absolute bottom-0 left-0 w-[2.4vw] h-[2.08vw] flex items-center justify-center bg-black/50 rounded-se-[.42vw] text-[1.3vw] text-white font-bold italic",
    className
  );
  return <div className={classes}>{index}</div>;
};

export default CardIndex;
