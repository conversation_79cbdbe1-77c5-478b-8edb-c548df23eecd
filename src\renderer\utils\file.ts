import pdf from "@assets/step-two/pdf.png";
import word from "@assets/step-two/word.png";

/**
 * 获取文件封面
 * @param type 文件类型
 * @returns 文件封面
 */
const getFileCover = (type: string, thumbnail?: string) => {
  switch (type) {
    case "pdf":
      return pdf;
    case "word":
      return word;
    case "image":
      return thumbnail;
  }
};

/**
 * 获取文件类型
 * @param path 文件路径
 * @returns 文件类型
 */
const getFileType = (path: string) => {
  const extension = "." + path.split(".").pop()?.toLowerCase();

  if (!extension) return "other";

  switch (extension) {
    case ".pdf":
      return "pdf";
    case ".doc":
    case ".docx":
      return "word";
    case ".xls":
    case ".xlsx":
      return "excel";
    case ".ppt":
    case ".pptx":
      return "ppt";
    case ".txt":
      return "txt";
    case ".jpg":
    case ".jpeg":
    case ".png":
      return "image";
    default:
      return "other";
  }
};

/**
 * 获取文件扩展名
 * @param url 文件URL
 * @returns 文件扩展名
 */
const getUrlExtension = (url: string) =>
  url.slice(((url.lastIndexOf(".") - 1) >>> 0) + 2);

export { getFileCover, getFileType, getUrlExtension };
