{"name": "showinfo-formo", "productName": "showinfo-formo", "version": "1.0.0", "description": "My Electron application description", "main": ".vite/build/main.js", "scripts": {"package": "electron-forge package", "make": "electron-forge make", "package:dongle": "cross-env DONGLE_CHECK=true electron-forge package", "make:dongle": "cross-env DONGLE_CHECK=true electron-forge make", "publish": "electron-forge publish", "dev": "cross-env DONGLE_CHECK=true electron-forge start", "lint": "eslint --ext .ts,.tsx .", "lint:fix": "eslint --ext .ts,.tsx . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "clean": "rimraf dist build out .vite", "clean:all": "pnpm clean && rimraf node_modules", "reinstall": "pnpm clean:all && pnpm install", "check": "pnpm type-check && pnpm lint && pnpm format:check", "precommit": "pnpm check", "version:patch": "npm version patch", "version:minor": "npm version minor", "version:major": "npm version major"}, "keywords": [], "author": {"name": "JingpengZhang", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-dmg": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron-forge/plugin-vite": "^7.8.1", "@electron/fuses": "^1.8.0", "@types/electron-squirrel-startup": "^1.0.2", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "electron": "36.5.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "~5.2.2", "vite": "^5.4.19", "vite-plugin-svgr": "^4.3.0"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@reduxjs/toolkit": "^2.8.2", "@types/md5": "^2.3.5", "@types/pdf-parse": "^1.1.5", "@types/ramda": "^0.30.2", "axios": "^1.10.0", "cross-env": "^10.0.0", "docx-preview": "^0.3.5", "drivelist": "^12.0.2", "electron-is-dev": "^3.0.1", "electron-squirrel-startup": "^1.0.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "md5": "^2.3.0", "mime": "^4.0.7", "motion": "^12.23.0", "nanoid": "^5.1.5", "pdf-parse": "^1.1.1", "pdf-to-printer": "^5.6.0", "pdfjs-dist": "^5.3.93", "qr-code-styling": "^1.9.2", "ramda": "^0.31.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "redux-persist": "^6.0.0", "tailwind-merge": "^3.3.1", "usb": "^2.15.0"}, "pnpm": {"onlyBuiltDependencies": ["drivelist", "electron", "electron-winstaller", "esbuild", "unrs-resolver", "usb"]}}