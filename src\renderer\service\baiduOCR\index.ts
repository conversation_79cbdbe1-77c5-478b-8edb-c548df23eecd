import { isRemoteUrl } from "@/main/utils/path";
import { OcrConfig } from "@/renderer/config/ocr";
import { baiduOCR } from "@/renderer/utils/request/factory";
import type {
  AccessTokenResponse,
  AnalysisPayload,
  AnalysisResponse,
  OCRAccurateBasicPayload,
  OCRAccurateBasicResponse,
  OCRPayload,
  OCRResponse,
  TaskAnalysisResponse,
} from "./types";

/**
 * 获取accessToken
 *
 * @description AI开放能力的所有能力，包括公有云接口和离线SDK不支持AI开放能力之外的产品
 *
 * @link https://cloud.baidu.com/doc/AI_REFERENCE/s/Km3zhy5t7
 * @returns
 */
const accessToken = () => {
  return baiduOCR.post<AccessTokenResponse>(
    `/oauth/2.0/token?grant_type=${OcrConfig.grant_type}&client_id=${OcrConfig.client_id}&client_secret=${OcrConfig.client_secret}`
  );
};

/**
 * 通用文字识别-高精度版
 *
 * @link https://cloud.baidu.com/doc/OCR/s/zk3h7y24b
 * @param token
 * @param payload
 * @returns
 */
const accurateBasic = (token: string, payload: OCRAccurateBasicPayload) => {
  return baiduOCR.post<OCRAccurateBasicResponse>(
    `/rest/2.0/ocr/v1/accurate_basic?access_token=${token}`,
    payload,
    {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
    }
  );
};

/**
 * 办公文档识别
 *
 * @link https://cloud.baidu.com/doc/OCR/s/zk3h7y24b
 */
const officeDocumentRecognition = (token: string, payload: OCRPayload) => {
  return baiduOCR.post<OCRResponse>(
    `/rest/2.0/ocr/v1/doc_analysis_office?access_token=${token}`,
    payload,
    {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
    }
  );
};

/**
 * 文档解析-提交请求
 *
 * @link https://cloud.baidu.com/doc/OCR/s/zk3h7y24b
 */
const documentAnalysis = (token: string, payload: AnalysisPayload) => {
  return baiduOCR.post<AnalysisResponse>(
    `/rest/2.0/brain/online/v2/parser/task?access_token=${token}`,
    payload,
    {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );
};

/**
 * 文档解析-获取结果
 *
 * @link https://cloud.baidu.com/doc/OCR/s/zk3h7y24b
 */
const taskAnalysis = (token: string, taskId: string) => {
  return baiduOCR.post<TaskAnalysisResponse>(
    `/rest/2.0/brain/online/v2/parser/task/query?access_token=${token}`,
    {
      task_id: taskId,
    },
    {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );
};

/**
 * 通用文字识别-高精度版-通过文件路径
 *
 * @link https://cloud.baidu.com/doc/OCR/s/zk3h7y24b
 * @param filePath
 * @returns
 */
const accurateBasicByFilePath = async (filePath: string) => {
  const { access_token } = await accessToken();

  const isRemote = isRemoteUrl(filePath);

  // 获取图片数据（本地转 base64，远程直接使用 URL）
  const imageData = isRemote
    ? filePath
    : await window.electronAPI.util.readFileAsBase64(filePath);

  // 构造请求参数
  const payload = isRemote ? { url: imageData } : { image: imageData };

  // 识别文字
  return await accurateBasic(access_token, payload);
};

/**
 * 文档解析-提交-获取结果
 *
 * @link https://cloud.baidu.com/doc/OCR/s/zk3h7y24b
 * @param filePath
 * @returns
 */
const pollDocumentAnalysis = async (filePath: string, fileName: string) => {
  const token = await accessToken();
  const fileData = await window.electronAPI.util.readFileAsBase64(filePath);
  // 文档解析-提交请求
  const response = await documentAnalysis(token.access_token, {
    file_name: fileName,
    file_data: fileData,
  });

  /**
   * 文档解析-获取结果
   * @param token
   * @param taskId
   * @returns
   */
  const pollTaskAnalysis = async (token: string, taskId: string) => {
    const interval = 1000;
    const maxAttempts = 5;
    const delayBeforeStart = 5000;

    if (delayBeforeStart > 0) {
      await new Promise(resolve => setTimeout(resolve, delayBeforeStart));
    }

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const res = await taskAnalysis(token, taskId);
        const status = res.result.status;

        if (status === "success") return res.result;
        if (status === "failed") return null;

        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, interval));
        }
      } catch (err) {
        if (attempt >= maxAttempts) {
          return null;
        }
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }

    return null;
  };

  // 文档解析-获取结果
  return await pollTaskAnalysis(token.access_token, response.result.task_id);
};

export {
  accessToken,
  accurateBasic,
  accurateBasicByFilePath,
  documentAnalysis,
  officeDocumentRecognition,
  pollDocumentAnalysis,
  taskAnalysis,
};
