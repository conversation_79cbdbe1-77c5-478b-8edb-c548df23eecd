import { addPublicPrefix } from "@/main/utils/path";
import type { FileInfo } from "@/types";
import { twMerge } from "tailwind-merge";
import ImagePreview from "./ImagePreview";
import NotSupport from "./NotSupport";
import PDFViewer from "./PDFViewer";
import WordPreview from "./WordViewer";

interface Props {
  className?: string;
  file: FileInfo;
  /** PDF 自定义样式 */
  pdfClassName?: string;
  /** Word 自定义样式 */
  wordClassName?: string;
  /** 骨架屏自定义样式 */
  skeletonClassName?: string;
}

const FileRender: React.FC<Props> = ({
  file,
  className = "w-[32.45vw] h-[45.78vw] overflow-y-auto",
  pdfClassName,
  skeletonClassName,
}) => {
  const { type, path, name } = file;

  const fullPath = addPublicPrefix(path, {
    remote: file?.remote,
  });

  const classes = twMerge("relative w-max", className);

  return (
    <div className={classes}>
      {type === "word" && (
        <WordPreview
          visible
          filePath={file.remote ? fullPath : path}
          fileName={name}
          skeletonClassName={twMerge("w-full h-[45.78vw]", skeletonClassName)}
        />
      )}
      {type === "pdf" && (
        <PDFViewer
          filePath={fullPath}
          fileName={name}
          mode="all"
          className={pdfClassName}
          skeletonClassName={skeletonClassName}
        />
      )}
      {type === "image" && <ImagePreview filePath={fullPath} fileName={name} />}
      {type === "other" && <NotSupport />}
    </div>
  );
};

export default FileRender;
