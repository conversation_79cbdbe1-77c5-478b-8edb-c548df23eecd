import React from "react";
import { twMerge } from "tailwind-merge";

const SplitLine: React.FC<{
  className?: string;
}> = ({ className }) => {
  const classes = twMerge(
    "h-[36.56vw] w-[6px] flex justify-center items-center",
    className
  );

  return (
    <div
      className={classes}
      style={{
        background: "linear-gradient(to bottom, #ffffff00, #3c5d8a, #ffffff00)",
      }}
    >
      <div
        className="h-2/3 w-[2px]"
        style={{
          background:
            "linear-gradient(to bottom, #55b0fe00, #55b0fe, #55b0fe00)",
          filter: "blur(1px)",
        }}
      />
    </div>
  );
};

export default SplitLine;
