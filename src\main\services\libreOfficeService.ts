import { ipcManager } from "@/main/ipc/IpcManager";
import { IPC_CHANNELS } from "@/shared/constants";
import { spawn, type ChildProcess } from "child_process";
import { app } from "electron";
import * as fs from "fs";
import * as http from "http";
import * as https from "https";
import * as path from "path";
import { URL } from "url";
import { promisify } from "util";
import { isRemoteUrl } from "../utils/path";

const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);
const unlinkAsync = promisify(fs.unlink);

/**
 * LibreOffice 转换选项
 */
export interface LibreOfficeOptions {
  /** 输入文件路径或远程URL */
  inputPath: string;
  /** 输出目录 */
  outputDir: string;
  /** 输出格式 (pdf, png, jpg) */
  format: "pdf" | "png" | "jpg";
  /** 是否生成缩略图 */
  thumbnail?: boolean;
  /** 缩略图尺寸 */
  thumbnailSize?: { width: number; height: number };
  /** 是否为远程文件 */
  isRemote?: boolean;
  /** 是否跳过转换 */
  skipped?: boolean;
}

/**
 * LibreOffice 转换结果
 */
export interface LibreOfficeResult {
  /** 是否成功 */
  success: boolean;
  /** 输出文件路径 */
  outputPath?: string;
  /** 缩略图路径 */
  thumbnailPath?: string;
  /** 错误信息 */
  error?: string;
  /** 转换耗时（毫秒） */
  duration?: number;
  /** 是否为远程文件转换 */
  isRemote?: boolean;
}

/**
 * LibreOffice 服务
 * 用于将 Word 文档转换为 PDF 或图片格式进行预览
 * 支持本地文件和远程文件
 */
class LibreOfficeService {
  private static instance: LibreOfficeService;
  private isConverting = false;
  private conversionQueue: Array<{
    options: LibreOfficeOptions;
    resolve: (result: LibreOfficeResult) => void;
    reject: (error: Error) => void;
  }> = [];
  private tempFiles: Set<string> = new Set(); // 跟踪临时文件

  private constructor() {
    this.setupIpcHandlers();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LibreOfficeService {
    if (!LibreOfficeService.instance) {
      LibreOfficeService.instance = new LibreOfficeService();
    }
    return LibreOfficeService.instance;
  }

  /**
   * 设置 IPC 处理器
   */
  private setupIpcHandlers(): void {
    // 转换文档为 PDF
    ipcManager.registerHandler(
      IPC_CHANNELS.LIBRE_OFFICE.CONVERT_TO_PDF,
      async (_, filePath: string) => {
        return await this.convertToPdf(filePath);
      }
    );

    // 转换文档为图片
    ipcManager.registerHandler(
      IPC_CHANNELS.LIBRE_OFFICE.CONVERT_TO_IMAGE,
      async (_, filePath: string, format: "png" | "jpg" = "png") => {
        return await this.convertToImage(filePath, format);
      }
    );

    // 生成文档缩略图
    ipcManager.registerHandler(
      IPC_CHANNELS.LIBRE_OFFICE.GENERATE_THUMBNAIL,
      async (_, filePath: string) => {
        return await this.generateThumbnail(filePath);
      }
    );

    // 检查 LibreOffice 是否可用
    ipcManager.registerHandler(
      IPC_CHANNELS.LIBRE_OFFICE.CHECK_AVAILABILITY,
      async () => {
        return await this.checkAvailability();
      }
    );
  }

  /**
   * 检查 LibreOffice 是否可用
   */
  public async checkAvailability(): Promise<boolean> {
    try {
      const command = this.getLibreOfficeCommand();
      const result = await this.executeCommand(command, ["--version"]);
      return result.success;
    } catch (error) {
      console.error("LibreOffice 检查失败:", error);
      return false;
    }
  }

  /**
   * 下载远程文件到本地临时目录
   */
  private async downloadRemoteFile(url: string): Promise<string> {
    const tempDir = path.join(process.cwd(), "temp", "downloads");
    await this.ensureDirectoryExists(tempDir);

    // 从URL中提取文件名
    const urlObj = new URL(url);
    const originalFileName = path.basename(urlObj.pathname);
    const fileExtension = path.extname(originalFileName);
    const fileName =
      originalFileName || `remote_file_${Date.now()}${fileExtension}`;
    const localPath = path.join(tempDir, fileName);

    return new Promise((resolve, reject) => {
      const protocol = urlObj.protocol === "https:" ? https : http;

      const request = protocol.get(url, response => {
        if (response.statusCode !== 200) {
          reject(new Error(`下载失败: HTTP ${response.statusCode}`));
          return;
        }

        const fileStream = fs.createWriteStream(localPath);
        response.pipe(fileStream);

        fileStream.on("finish", () => {
          fileStream.close();
          this.tempFiles.add(localPath); // 添加到临时文件跟踪
          console.log(`远程文件下载完成: ${url} -> ${localPath}`);
          resolve(localPath);
        });

        fileStream.on("error", error => {
          fs.unlink(localPath, () => {}); // 删除不完整的文件
          reject(new Error(`文件写入失败: ${error.message}`));
        });
      });

      request.on("error", error => {
        reject(new Error(`网络请求失败: ${error.message}`));
      });

      request.setTimeout(30000, () => {
        request.destroy();
        reject(new Error("下载超时"));
      });
    });
  }

  /**
   * 转换文档为 PDF
   */
  public async convertToPdf(filePath: string): Promise<LibreOfficeResult> {
    const outputDir = path.join(process.cwd(), "temp", "pdf");
    await this.ensureDirectoryExists(outputDir);

    const isRemote = isRemoteUrl(filePath);
    let localFilePath = filePath;

    // 如果是远程文件，先下载到本地
    if (isRemote) {
      try {
        console.log(`开始下载远程文件: ${filePath}`);
        localFilePath = await this.downloadRemoteFile(filePath);
      } catch (error) {
        return {
          success: false,
          error: `远程文件下载失败: ${error}`,
          isRemote: true,
        };
      }
    }

    // 检查源文件是否为pdf
    const ext = path.extname(localFilePath).toLowerCase();

    const options: LibreOfficeOptions = {
      inputPath: localFilePath,
      outputDir,
      format: "pdf",
      isRemote,
      skipped: ext === ".pdf",
    };

    // 远程链接为pdf文件不进行转换
    if (ext === ".pdf") {
      return {
        success: true,
        outputPath: localFilePath,
        isRemote,
      };
    }

    return await this.convertDocument(options);
  }

  /**
   * 转换文档为图片
   */
  public async convertToImage(
    filePath: string,
    format: "png" | "jpg" = "png"
  ): Promise<LibreOfficeResult> {
    const outputDir = path.join(process.cwd(), "temp", "images");
    await this.ensureDirectoryExists(outputDir);

    const isRemote = isRemoteUrl(filePath);
    let localFilePath = filePath;

    // 如果是远程文件，先下载到本地
    if (isRemote) {
      try {
        console.log(`开始下载远程文件: ${filePath}`);
        localFilePath = await this.downloadRemoteFile(filePath);
      } catch (error) {
        return {
          success: false,
          error: `远程文件下载失败: ${error}`,
          isRemote: true,
        };
      }
    }

    const options: LibreOfficeOptions = {
      inputPath: localFilePath,
      outputDir,
      format,
      isRemote,
    };

    return await this.convertDocument(options);
  }

  /**
   * 生成文档缩略图
   */
  public async generateThumbnail(filePath: string): Promise<LibreOfficeResult> {
    const outputDir = path.join(process.cwd(), "temp", "thumbnails");
    await this.ensureDirectoryExists(outputDir);

    const isRemote = isRemoteUrl(filePath);
    let localFilePath = filePath;

    // 如果是远程文件，先下载到本地
    if (isRemote) {
      try {
        console.log(`开始下载远程文件: ${filePath}`);
        localFilePath = await this.downloadRemoteFile(filePath);
      } catch (error) {
        return {
          success: false,
          error: `远程文件下载失败: ${error}`,
          isRemote: true,
        };
      }
    }

    const options: LibreOfficeOptions = {
      inputPath: localFilePath,
      outputDir,
      format: "png",
      thumbnail: true,
      thumbnailSize: { width: 200, height: 150 },
      isRemote,
    };

    return await this.convertDocument(options);
  }

  /**
   * 转换文档
   */
  private async convertDocument(
    options: LibreOfficeOptions
  ): Promise<LibreOfficeResult> {
    return new Promise((resolve, reject) => {
      this.conversionQueue.push({ options, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * 处理转换队列
   */
  private async processQueue(): Promise<void> {
    if (this.isConverting || this.conversionQueue.length === 0) {
      return;
    }

    this.isConverting = true;
    const item = this.conversionQueue.shift();
    if (!item) {
      return;
    }
    const { options, resolve, reject } = item;

    try {
      const startTime = Date.now();
      const result = await this.performConversion(options);
      result.duration = Date.now() - startTime;
      result.isRemote = options.isRemote;
      resolve(result);
    } catch (error) {
      reject(error as Error);
    } finally {
      this.isConverting = false;
      // 处理队列中的下一个任务
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * 执行转换
   */
  private async performConversion(
    options: LibreOfficeOptions
  ): Promise<LibreOfficeResult> {
    const { inputPath, outputDir, format, thumbnail, thumbnailSize } = options;

    // 检查输入文件是否存在
    if (!(await existsAsync(inputPath))) {
      return {
        success: false,
        error: `输入文件不存在: ${inputPath}`,
        isRemote: options.isRemote,
      };
    }

    // 生成输出文件名
    const inputFileName = path.basename(inputPath, path.extname(inputPath));
    const outputFileName = thumbnail
      ? `${inputFileName}_thumb.${format}`
      : `${inputFileName}.${format}`;
    const outputPath = path.join(outputDir, outputFileName);

    // 构建 LibreOffice 命令参数
    const command = this.getLibreOfficeCommand();
    const args = this.buildLibreOfficeArgs(
      inputPath,
      outputDir,
      format,
      thumbnail,
      thumbnailSize
    );

    try {
      const result = await this.executeCommand(command, args);

      if (result.success) {
        // 检查输出文件是否生成
        if (await existsAsync(outputPath)) {
          return {
            success: true,
            outputPath,
            thumbnailPath: thumbnail ? outputPath : undefined,
            isRemote: options.isRemote,
          };
        } else {
          return {
            success: false,
            error: "转换成功但输出文件未找到",
            isRemote: options.isRemote,
          };
        }
      } else {
        return {
          success: false,
          error: result.error || "转换失败",
          isRemote: options.isRemote,
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `转换异常: ${error}`,
        isRemote: options.isRemote,
      };
    }
  }

  /**
   * 获取 LibreOffice 命令
   */
  private getLibreOfficeCommand(): string {
    // 获取当前应用的资源目录
    const resourcesPath = app.isPackaged
      ? path.join(process.resourcesPath, "resources") // 打包后
      : path.join(app.getAppPath(), "resources"); // 开发时

    if (process.platform === "win32") {
      const localLibrePath = path.join(
        resourcesPath,
        "libreoffice",
        "win",
        "program",
        "soffice.bin"
      );

      if (fs.existsSync(localLibrePath)) return localLibrePath;

      const possiblePaths = [
        "C:\\Program Files\\LibreOffice\\program\\soffice.bin",
        "C:\\Program Files (x86)\\LibreOffice\\program\\soffice.bin",
      ];

      for (const p of possiblePaths) {
        if (fs.existsSync(p)) return p;
      }

      return "soffice.exe"; // fallback to PATH
    }

    // macOS 默认路径
    if (process.platform === "darwin") {
      const macPath = "/Applications/LibreOffice.app/Contents/MacOS/soffice";
      return fs.existsSync(macPath) ? macPath : "soffice";
    }

    // Linux or others
    return "libreoffice";
  }

  /**
   * 构建 LibreOffice 命令参数
   */
  private buildLibreOfficeArgs(
    inputPath: string,
    outputDir: string,
    format: string,
    thumbnail?: boolean,
    thumbnailSize?: { width: number; height: number }
  ): string[] {
    const args = [
      "--headless", // 无界面模式
      "--convert-to",
      format, // 转换格式
      "--outdir",
      outputDir, // 输出目录
    ];

    // 如果是生成缩略图，添加相关参数
    if (thumbnail && thumbnailSize) {
      args.push("--infilter", "Impress MS PowerPoint 2007 XML");
      args.push("--convert-images-to", "png");
    }

    args.push(inputPath);
    return args;
  }

  /**
   * 执行命令
   */
  private async executeCommand(
    command: string,
    args: string[]
  ): Promise<{ success: boolean; error?: string }> {
    return new Promise(resolve => {
      const process: ChildProcess = spawn(command, args, {
        stdio: ["pipe", "pipe", "pipe"],
        // shell: true,
      });

      let stderr = "";

      process.stderr?.on("data", data => {
        stderr += data.toString();
      });

      process.on("close", code => {
        if (code === 0) {
          resolve({ success: true });
        } else {
          resolve({
            success: false,
            error: stderr || `命令执行失败，退出码: ${code}`,
          });
        }
      });

      process.on("error", error => {
        resolve({
          success: false,
          error: `命令执行错误: ${error.message}`,
        });
      });

      // 设置超时
      setTimeout(() => {
        process.kill();
        resolve({
          success: false,
          error: "命令执行超时",
        });
      }, 30000); // 30秒超时
    });
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    if (!(await existsAsync(dirPath))) {
      await mkdirAsync(dirPath, { recursive: true });
    }
  }

  /**
   * 清理临时文件
   */
  public async cleanupTempFiles(): Promise<void> {
    const tempDirs = [
      path.join(process.cwd(), "temp", "pdf"),
      path.join(process.cwd(), "temp", "images"),
      path.join(process.cwd(), "temp", "thumbnails"),
      path.join(process.cwd(), "temp", "downloads"), // 新增下载目录
    ];

    for (const dir of tempDirs) {
      try {
        if (await existsAsync(dir)) {
          const files = await promisify(fs.readdir)(dir);
          for (const file of files) {
            const filePath = path.join(dir, file);
            await unlinkAsync(filePath);
          }
        }
      } catch (error) {
        console.error(`清理临时文件失败: ${dir}`, error);
      }
    }

    // 清理跟踪的临时文件
    for (const tempFile of this.tempFiles) {
      try {
        if (await existsAsync(tempFile)) {
          await unlinkAsync(tempFile);
          console.log(`清理临时文件: ${tempFile}`);
        }
      } catch (error) {
        console.error(`清理临时文件失败: ${tempFile}`, error);
      }
    }
    this.tempFiles.clear();
  }

  /**
   * 获取临时文件数量
   */
  public getTempFileCount(): number {
    return this.tempFiles.size;
  }

  /**
   * 手动清理特定临时文件
   */
  public async cleanupSpecificTempFile(filePath: string): Promise<boolean> {
    try {
      if (await existsAsync(filePath)) {
        await unlinkAsync(filePath);
        this.tempFiles.delete(filePath);
        console.log(`清理特定临时文件: ${filePath}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`清理特定临时文件失败: ${filePath}`, error);
      return false;
    }
  }
}

export default LibreOfficeService;
