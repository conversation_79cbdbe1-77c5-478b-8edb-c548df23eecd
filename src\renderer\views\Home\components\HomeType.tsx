import { HomeType as HomeTypeEnum } from "@/types/views/home";
import { motion } from "motion/react";
import type React from "react";

interface Props {
  typeList: { id: HomeTypeEnum; name: string; img: string }[];
  onClick: (id: HomeTypeEnum) => void;
}

const HomeType: React.FC<Props> = ({ typeList, onClick }) => {
  return (
    <div className="grid grid-cols-5 gap-[.9vw] cursor-pointer">
      {typeList.map((item, index) => (
        <motion.img
          key={item.id}
          src={item.img}
          alt={item.name}
          className="h-[16.35vw] object-contain"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            default: { duration: 0.3, ease: "easeOut", delay: index * 0.1 },
            scale: { duration: 0.2, ease: "easeInOut" },
          }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => onClick(item.id)}
          draggable={false}
        />
      ))}
    </div>
  );
};

export default HomeType;
