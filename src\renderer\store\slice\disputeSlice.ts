import type {
  DisputeForm,
  DisputeFormPayload,
  DisputeFormv2,
  Formv2Item,
  PackageItem,
} from "@/renderer/service/dispute/types";
import type { DisputeFormV2Payload } from "@/types/views/dispute";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getDisputeForm, getDisputeFormv2 } from "../../service/dispute";

/**
 * 纠纷数据状态接口
 */
interface DisputeState {
  /** 纠纷表单数据列表 */
  forms: DisputeForm[];
  /** 纠纷表单数据列表 v2 */
  formsV2: Formv2Item[];
  /** 纠纷表单数据列表 v2 分页数据 */
  formsV2Map: Record<number, Formv2Item[]>;
  /** 套餐功能项数据列表 */
  packages: PackageItem[];
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 是否已初始化 */
  initialized: boolean;
  /** 最后更新时间 */
  lastUpdated: number | null;
  pagination: {
    /** 总数 */
    total: number;
    /** 当前页 */
    current: number;
    /** 总页 */
    totalPage: number;
    /** 每页数据 */
    pageSize: number;
  };
}

/**
 * 初始状态
 */
const initialState: DisputeState = {
  forms: [],
  formsV2: [],
  formsV2Map: {},
  packages: [],
  loading: false,
  error: null,
  initialized: false,
  lastUpdated: null,
  pagination: {
    total: 0,
    current: 1,
    totalPage: 1,
    pageSize: 10,
  },
};

/**
 * 获取纠纷表单数据
 */
export const fetchDisputeForms = createAsyncThunk<
  DisputeForm[],
  DisputeFormPayload,
  { rejectValue: string }
>(
  "dispute/fetchForms",
  async (payload, { rejectWithValue }) => {
    try {
      const response = await getDisputeForm(payload);
      return response.data;
    } catch (error) {
      // 错误处理和监控
      console.error("获取纠纷表单数据失败:", error);
      return rejectWithValue(
        error instanceof Error ? error.message : "获取纠纷表单数据失败"
      );
    }
  },
  {
    // 如果数据已存在且未过期，则不重复请求
    condition: (_, { getState }) => {
      const state = getState() as { dispute: DisputeState };
      const { forms, lastUpdated, initialized } = state.dispute;

      // 如果已初始化且有数据，且数据未过期（5分钟内），则不重复请求
      if (initialized && forms.length > 0 && lastUpdated) {
        const now = Date.now();
        const fiveMinutes = 5 * 60 * 1000;
        if (now - lastUpdated < fiveMinutes) {
          return false;
        }
      }
      return true;
    },
  }
);

/**
 * 获取纠纷表单数据
 */
export const fetchDisputeFormsV2 = createAsyncThunk<
  DisputeFormv2,
  DisputeFormV2Payload,
  { rejectValue: string }
>(
  "dispute/fetchFormsv2",
  async (payload, { rejectWithValue }) => {
    try {
      const { limit, page, mid } = payload;
      const response = await getDisputeFormv2({ limit, page, mid });
      return response.data;
    } catch (error) {
      // 错误处理和监控
      console.error("获取纠纷表单数据失败:", error);
      return rejectWithValue(
        error instanceof Error ? error.message : "获取纠纷表单数据失败"
      );
    }
  },
  {
    // 如果数据已存在且未过期，则不重复请求
    condition: (payload, { getState }) => {
      const state = getState() as { dispute: DisputeState };

      const { lastUpdated, initialized } = state.dispute;
      const page = payload.page;

      // 如果强制刷新，则跳过缓存判断
      if (payload.force) return true;

      // 如果已有该页数据，且未过期，则跳过
      const last = lastUpdated;
      if (initialized && page && state.dispute.formsV2Map[page] && last) {
        const now = Date.now();
        const fiveMinutes = 5 * 60 * 1000;
        if (now - last < fiveMinutes) return false;
      }

      return true;
    },
  }
);

/**
 * 纠纷数据
 */
const disputeSlice = createSlice({
  name: "dispute",
  initialState,
  reducers: {
    /**
     * 设置每页数据
     */
    updatePagination(
      state,
      action: PayloadAction<Partial<DisputeState["pagination"]>>
    ) {
      state.pagination = {
        ...state.pagination,
        ...action.payload,
      };
    },

    /**
     * 清除错误信息
     */
    clearError: state => {
      state.error = null;
    },

    /**
     * 重置状态
     */
    resetDispute: state => {
      state.forms = [];
      state.loading = false;
      state.error = null;
      state.initialized = false;
      state.lastUpdated = null;
      state.pagination = {
        total: 0,
        totalPage: 1,
        current: 1,
        pageSize: 10,
      };
    },

    /**
     * 手动设置纠纷表单数据（用于测试或预加载）
     */
    setDisputeForms: (state, action: PayloadAction<DisputeForm[]>) => {
      state.forms = action.payload;
      state.initialized = true;
      state.lastUpdated = Date.now();
      state.error = null;
    },

    /**
     * 更新单个纠纷表单的使用次数
     */
    updateFormUseCount: (
      state,
      action: PayloadAction<{ id: number; useCount: number }>
    ) => {
      const { id, useCount } = action.payload;
      const form = state.forms.find(f => f.id === id);
      if (form) {
        form.use_count = useCount;
      }
    },

    /**
     * 更新单个纠纷表单的打印次数
     */
    updateFormPrintCount: (
      state,
      action: PayloadAction<{ id: number; printCount: number }>
    ) => {
      const { id, printCount } = action.payload;
      const form = state.forms.find(f => f.id === id);
      if (form) {
        form.print_count = printCount;
      }
    },
  },
  extraReducers: builder => {
    builder
      // 获取纠纷表单数据 - 开始
      .addCase(fetchDisputeForms.pending, state => {
        state.loading = true;
        state.error = null;
      })
      // 获取纠纷表单数据 - 成功
      .addCase(fetchDisputeForms.fulfilled, (state, action) => {
        state.loading = false;
        state.forms = action.payload;
        state.initialized = true;
        state.lastUpdated = Date.now();
        state.error = null;
      })
      // 获取纠纷表单数据 - 失败
      .addCase(fetchDisputeForms.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "获取纠纷表单数据失败";
        console.error("Redux: 获取纠纷表单数据失败", action.error);
      });
    builder
      // 获取纠纷表单数据 - 开始
      .addCase(fetchDisputeFormsV2.pending, state => {
        state.loading = true;
        state.error = null;
      })
      // 获取纠纷表单数据 - 成功
      .addCase(fetchDisputeFormsV2.fulfilled, (state, action) => {
        const { anyou, items, total, current_page } = action.payload;

        state.loading = false;
        state.initialized = true;
        state.lastUpdated = Date.now();
        state.error = null;
        state.pagination.total = total;
        state.pagination.current = current_page;
        state.pagination.totalPage = Math.ceil(
          total / state.pagination.pageSize
        );

        // 设置当前页数据（可选：追加模式 append）
        if (action.meta.arg.append && state.formsV2Map[current_page]) {
          state.formsV2Map[current_page] = [
            ...state.formsV2Map[current_page],
            ...anyou,
          ];
        } else {
          state.formsV2Map[current_page] = anyou;
        }

        // 当前页显示用（也可以删除，仅用 formsV2Map）
        state.formsV2 = state.formsV2Map[current_page];

        // 套餐功能项数据为全局，不分页
        state.packages = items;
      })
      // 获取纠纷表单数据 - 失败
      .addCase(fetchDisputeFormsV2.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "获取纠纷表单数据失败";
        console.error("Redux: 获取纠纷表单数据失败", action.error);
      });
  },
});

export const {
  updatePagination,
  clearError,
  resetDispute,
  setDisputeForms,
  updateFormUseCount,
  updateFormPrintCount,
} = disputeSlice.actions;

export const selectDisputeForms = (state: { dispute: DisputeState }) =>
  state.dispute.forms;
export const selectDisputeFormsV2 = (state: { dispute: DisputeState }) =>
  state.dispute.formsV2;
export const selectDisputePackages = (state: { dispute: DisputeState }) =>
  state.dispute.packages;
export const selectDisputeLoading = (state: { dispute: DisputeState }) =>
  state.dispute.loading;
export const selectDisputeError = (state: { dispute: DisputeState }) =>
  state.dispute.error;
export const selectDisputeInitialized = (state: { dispute: DisputeState }) =>
  state.dispute.initialized;
export const selectDisputeLastUpdated = (state: { dispute: DisputeState }) =>
  state.dispute.lastUpdated;

export default disputeSlice.reducer;
