import React from "react";
import { twMerge } from "tailwind-merge";

interface PDFSkeletonProps {
  /** 缩放比例 */
  scale?: number;
  /** 自定义样式 */
  className?: string;
}

/**
 * PDF页面骨架屏组件
 */
const PDFSkeleton: React.FC<PDFSkeletonProps> = ({ scale = 1, className }) => {
  return (
    <div
      className={twMerge(
        "flex justify-center overflow-hidden w-full h-full",
        className
      )}
    >
      <div
        className="shadow-lg bg-white animate-pulse overflow-hidden"
        style={{
          width: "100%",
          height: "100%",
          transform: `scale(${scale})`,
          transformOrigin: "top center",
        }}
      >
        {/* 页面内容骨架 */}
        <div className="h-full flex flex-col p-8">
          {/* 页面标题骨架 */}
          <div className="h-6 bg-gray-200 rounded mb-4 w-3/4"></div>

          {/* 段落骨架 */}
          <div className="space-y-3 flex-1">
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/5"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>

          {/* 页面底部信息骨架 */}
          <div className="flex justify-between items-center mt-4">
            <div className="h-3 bg-gray-200 rounded w-20"></div>
            <div className="h-3 bg-gray-200 rounded w-16"></div>
          </div>
        </div>

        {/* 页面边框 */}
        <div className="absolute inset-0 border border-gray-300 pointer-events-none"></div>
      </div>
    </div>
  );
};

export default PDFSkeleton;
