import { ipcManager } from "@/main/ipc/IpcManager";
import { getOptimalResolution, RESOLUTIONS } from "@/main/utils";
import { IPC_CHANNELS } from "@/shared/constants";
import { BrowserWindow } from "electron";
import path from "path";
import type { IWindowManager, WindowState } from "./types";

/**
 * 主窗口管理类
 * 负责主窗口的创建、配置、事件处理和状态管理
 */
export class MainWindow implements IWindowManager {
  private window: BrowserWindow | null = null;
  private isDestroyed = false;

  /**
   * 创建主窗口
   */
  public create(): BrowserWindow {
    if (this.window && !this.window.isDestroyed()) {
      return this.window;
    }

    const { width, height } = getOptimalResolution();

    this.window = new BrowserWindow({
      width,
      height,
      minWidth: RESOLUTIONS.HD.width, // 设置最小宽度
      minHeight: RESOLUTIONS.HD.height, // 设置最小高度
      /** 去掉边框和标题栏 */
      frame: true,
      /**  macOS 兼容 */
      titleBarStyle: "default",
      /** 全屏幕 */
      fullscreen: true,
      /** 允许窗口缩放 */
      resizable: true,
      show: false, // 初始隐藏，等待ready-to-show事件
      webPreferences: {
        preload: path.join(__dirname, "preload.js"),
        contextIsolation: true,
      },
    });

    this.setupEventListeners();
    this.setupIpcHandlers();
    this.loadContent();

    return this.window;
  }

  /**
   * 设置窗口事件监听器
   */
  private setupEventListeners(): void {
    if (!this.window) return;

    // 窗口准备显示时显示窗口
    this.window.once("ready-to-show", () => {
      this.window?.show();
    });

    // 监听窗口大小变化，保持16:9比例
    this.window.on("resize", this.handleResize.bind(this));

    // 监听窗口关闭事件
    this.window.on("closed", () => {
      this.window = null;
      this.isDestroyed = true;
    });
  }

  /**
   * 设置窗口控制相关的IPC处理器
   * 将窗口控制功能内聚到窗口类中
   */
  private setupIpcHandlers(): void {
    // 最小化窗口
    ipcManager.registerListener(IPC_CHANNELS.WINDOW.MINIMIZE, () => {
      this.minimize();
    });

    // 最大化窗口
    ipcManager.registerListener(IPC_CHANNELS.WINDOW.MAXIMIZE, () => {
      this.maximize();
    });

    // 恢复窗口
    ipcManager.registerListener(IPC_CHANNELS.WINDOW.RESTORE, () => {
      this.restore();
    });

    // 关闭窗口
    ipcManager.registerListener(IPC_CHANNELS.WINDOW.CLOSE, () => {
      this.close();
    });

    // 检查窗口是否最大化
    ipcManager.registerHandler(IPC_CHANNELS.WINDOW.IS_MAXIMIZED, () => {
      return this.isMaximized();
    });

    // 检查窗口是否最小化
    ipcManager.registerHandler(IPC_CHANNELS.WINDOW.IS_MINIMIZED, () => {
      return this.isMinimized();
    });
  }

  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    if (!this.window) return;

    const [currentWidth, currentHeight] = this.window.getSize();

    // 确保不小于最小尺寸
    if (currentWidth < RESOLUTIONS.HD.width) {
      this.window.setSize(RESOLUTIONS.HD.width, RESOLUTIONS.HD.height);
      return;
    }

    // 计算16:9比例的高度
    const newHeight = Math.round(currentWidth / (16 / 9));

    // 仅在高度变化超过5像素时调整，避免频繁触发
    if (Math.abs(currentHeight - newHeight) > 5) {
      this.window.setSize(currentWidth, newHeight);
    }
  }

  /**
   * 加载应用内容
   */
  private loadContent(): void {
    if (!this.window) return;

    // 加载应用
    if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
      this.window.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
    } else {
      this.window.loadFile(
        path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`)
      );
    }
  }

  /**
   * 获取窗口实例
   */
  public getWindow(): BrowserWindow | null {
    return this.window;
  }

  /**
   * 窗口是否已创建且未销毁
   */
  public isReady(): boolean {
    return this.window !== null && !this.window.isDestroyed();
  }

  /**
   * 显示窗口
   */
  public show(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.show();
    }
  }

  /**
   * 隐藏窗口
   */
  public hide(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.hide();
    }
  }

  /**
   * 关闭窗口
   */
  public close(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.close();
    }
  }

  /**
   * 设置窗口大小
   */
  public setSize(width: number, height: number): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.setSize(width, height);
    }
  }

  /**
   * 获取窗口大小
   */
  public getSize(): { width: number; height: number } {
    if (this.window && !this.window.isDestroyed()) {
      const [width, height] = this.window.getSize();
      return { width, height };
    }
    return { width: 0, height: 0 };
  }

  /**
   * 窗口是否已销毁
   */
  public get destroyed(): boolean {
    return this.isDestroyed || !this.window || this.window.isDestroyed();
  }

  /**
   * 获取窗口状态
   */
  public getState(): WindowState {
    const isWindowReady = this.isReady();
    const size = this.getSize();

    return {
      isReady: isWindowReady,
      isVisible: isWindowReady ? (this.window?.isVisible() ?? false) : false,
      isDestroyed: this.destroyed,
      width: size.width,
      height: size.height,
    };
  }

  /**
   * 最小化窗口
   */
  public minimize(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.minimize();
    }
  }

  /**
   * 最大化窗口
   */
  public maximize(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.maximize();
    }
  }

  /**
   * 恢复窗口
   */
  public restore(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.restore();
    }
  }

  /**
   * 窗口是否最大化
   */
  public isMaximized(): boolean {
    return this.window ? this.window.isMaximized() : false;
  }

  /**
   * 窗口是否最小化
   */
  public isMinimized(): boolean {
    return this.window ? this.window.isMinimized() : false;
  }
}

// 导出单例实例
export const mainWindow = new MainWindow();
