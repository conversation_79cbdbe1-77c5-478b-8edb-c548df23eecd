import { AnimatePresence, motion } from "framer-motion";

type Props = {
  type: "card" | "pass";
  onChange: (type: "card" | "pass") => void;
};

const ChangePrint: React.FC<Props> = ({ type, onChange }) => {
  const handleClick = () => {
    onChange(type === "card" ? "pass" : "card");
  };

  return (
    <div
      className={
        "top-[2vw] left-[1vw] w-[10vw] h-[3vw] flex items-center justify-center text-white rounded-full border-[2px] border-solid border-[#3669b8] cursor-pointer relative overflow-hidden"
      }
      style={{
        background: "rgba(29, 89, 180, 0.2)",
      }}
      onClick={handleClick}
    >
      {/* 文字内容 */}
      <div className="relative z-10 overflow-hidden">
        <AnimatePresence mode="wait">
          <motion.div
            key={type}
            className="text-[1vw] font-medium"
            variants={{
              initial: {
                opacity: 0,
                scale: 0.8,
                rotateY: -40,
                y: 20,
              },
              animate: {
                opacity: 1,
                scale: 1,
                rotateY: 0,
                y: 0,
              },
              exit: {
                opacity: 0,
                scale: 0.8,
                rotateY: 40,
                y: -20,
                transition: {
                  duration: 0.3,
                  ease: "easeIn" as const,
                },
              },
            }}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            {type === "card" ? "切换密码打印" : "切换身份证打印"}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ChangePrint;
