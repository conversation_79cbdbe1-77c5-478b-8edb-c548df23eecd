import CheckBox from "@/renderer/components/CheckBox";
import { getFileCover } from "@/renderer/utils/file";
import React from "react";
import { twMerge } from "tailwind-merge";

interface UsbFileProps {
  active?: boolean;
  type: "pdf" | "word" | "excel" | "ppt" | "txt" | "image" | "other";
  name?: string;
  thumbnail?: string;
  onChange?: (active: boolean) => void;
}

const UsbFile: React.FC<UsbFileProps> = ({
  type,
  name,
  active = true,
  thumbnail,
  onChange,
}) => {
  const classes = twMerge(
    "relative flex justify-center items-center w-full h-[7.7vw] rounded-[.6vw]",
    active
      ? "from-deepOcean-default to-brightSky-default bg-gradient-to-t before:content-[''] before:absolute before:top-[2px] before:left-[2px] before:w-[calc(100%-4px)] before:h-[calc(100%-4px)] before:bg-[#142b4f] before:rounded-[.6vw] before:z-[1] before:transition-all before:duration-300 before:ease-in-out"
      : "bg-deepOcean-default"
  );

  return (
    <div
      className="w-[7.7vw] h-max flex flex-col items-center gap-[.72vw] cursor-pointer"
      onClick={() => {
        onChange?.(!active);
      }}
    >
      <div className={classes}>
        <img
          src={getFileCover(type, thumbnail)}
          alt=""
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 h-[5.365vw] object-contain z-[1] p-[4px]"
          draggable={false}
        />

        <CheckBox
          className="absolute right-[2px] bottom-[2px] z-[1]"
          value={active}
        />
      </div>

      <div className="w-full overflow-hidden text-primary-70 text-center px-[10px] truncate">
        {name}
      </div>
    </div>
  );
};

export default UsbFile;
