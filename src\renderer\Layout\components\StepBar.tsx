import QRCodeMask from "@/renderer/components/QRCodeMask";
import { getFeedBackUrl } from "@/renderer/config/domain";
import { useAppSelector } from "@/renderer/store/hooks";
import Back from "@assets/home/<USER>";
import feed from "@assets/step-four/feed.png";
import ImgStepFout from "@assets/step/step_four.png";
import ImgStepOne from "@assets/step/step_one.png";
import ImgStepThree from "@assets/step/step_three.png";
import ImgStepTwo from "@assets/step/step_two.png";
import ImgStepTwoScan from "@assets/step/step_two_scan.png";
import ImgStepTwoU from "@assets/step/step_two_u.png";
import { motion } from "motion/react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
export enum Step {
  One = 1,
  Two = 2,
  TwoU = 2.2,
  TwoScan = 2.5,
  Three = 3,
  Four = 4,
}

type Props = {
  step?: Step;
};

const StepBar: React.FC<Props> = ({ step }) => {
  const navigate = useNavigate();
  const mid = useAppSelector(state => state.client?.mid);

  const getStepImage = (step?: number) => {
    switch (step) {
      case Step.One:
        return ImgStepOne;
      case Step.Two:
        return ImgStepTwo;
      case Step.TwoScan:
        return ImgStepTwoScan;
      case Step.TwoU:
        return ImgStepTwoU;
      case Step.Three:
        return ImgStepThree;
      case Step.Four:
        return ImgStepFout;
      default:
        return ImgStepOne;
    }
  };

  const [qrCodeVisible, setQrCodeVisible] = useState(false);

  // 意见反馈
  const handleFeedBack = () => {
    setQrCodeVisible(true);
  };

  return (
    <div className="relative warp mt-[4vh]">
      {/* todo：使用css实现 */}
      <div className="absolute top-1/2 -translate-y-1/2 right-[13vw]">
        {step === Step.One && (
          <motion.img
            src={Back}
            alt="back"
            className="cursor-pointer h-full object-contain"
            whileTap={{ scale: 0.9 }}
            transition={{ duration: 0.2, ease: "easeInOut" }}
            draggable={false}
            onClick={() => {
              navigate("/");
            }}
          />
        )}
        {step === Step.Four && (
          <motion.img
            src={feed}
            alt="feed"
            className="cursor-pointer h-full object-contain mr-[10px]"
            whileTap={{ scale: 0.9 }}
            transition={{ duration: 0.2, ease: "easeInOut" }}
            draggable={false}
            onClick={handleFeedBack}
          />
        )}
      </div>

      <img
        src={getStepImage(step)}
        alt=""
        className="w-full h-fit"
        draggable={false}
      />

      <QRCodeMask
        value={getFeedBackUrl(mid ?? 0)}
        visible={qrCodeVisible}
        title="意见反馈"
        desc="您可以通过手机扫码向我们反馈意见"
        onClose={() => {
          setQrCodeVisible(false);
        }}
      />
    </div>
  );
};

export default StepBar;
