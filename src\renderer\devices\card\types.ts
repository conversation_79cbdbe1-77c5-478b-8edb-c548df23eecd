export type ConnectType = "COM" | "USB" | "NET";

export type CommandCode =
  | "boyayingjie_ReadSAM" // 读取SAM码
  | "boyayingjie_Single" // 读取身份证信息（方式一）
  | "boyayingjie_Continuity" // 连续读取身份证信息（方式二）
  | "boyayingjie_Stop" // 取消连续读取身份证信息
  | "boyayingjie_Fingerprint" // 读取身份证指纹
  | "boyayingjie_OpenAntenna" // 开天线（二合一）
  | "boyayingjie_OFFAntenna" // 关天线（二合一）
  | "boyayingjie_ReadUserID" // 身份证UID读取
  | "boyayingjie_ICNumber" // 读取IC卡号
  | "boyayingjie_ICVerify" // 核验IC卡密码
  | "boyayingjie_ReadSector" // 读IC卡数据
  | "boyayingjie_WriteSector"; // 写IC卡数据

/**
 * 基础响应
 */
interface BaseResponse<T = string> {
  /** 姓名 */
  Name: T;
  /** 性别 */
  Sex: T;
  /** 民族 */
  Nation: T;
  /** 民族代码 */
  NationCode: T;
  /** 生日 */
  Birthday: T;
  /** 地址 */
  Address: T;
  /** 证件类型 */
  CardType: T;
  /** 证件号码 */
  IDNumber: T;
  /** 签发机关 */
  IDIssued: T;
  /** 签发日期 */
  IssuedData: T;
  /** 截止日期 */
  ValidDate: T;
  /** 照片Base64数据 */
  Base64Photo: T;
  /** 指纹Base64数据 */
  Base64Fingerprint: T;
  /** 签发次数 */
  IssueNum: T;
  /** 通行证号 */
  PassNum: T;
  /** 中文名 */
  ChineseName: T;
  /** 居住证号码 */
  ResidenceNum: T;
  /** 证件版本号 */
  VersionNum: T;
  /** 国籍区域代码 */
  Nationality: T;
  /** 耗时 */
  Time: T;
  /** 异常信息 */
  ErrorString: T;
}

/**
 * 读取接口请求
 */
export interface ReadRequest {
  /** 连接类型 */
  ConnectType: ConnectType;
  /** 连接字符串 */
  ConnectStr: string;
  [key: string]: any;
}

/**
 * 读取SAM码接口返回
 */
export interface ReadSAMResponse extends BaseResponse {
  /** 连接状态（如 "COM5"） */
  ConnectType: ConnectType;
  /** SAM码（安全模块序列号） */
  SAM: string;
  /** 身份证ID */
  UserID: null;
}

/**
 * 读取身份证信息接口请求
 */
export interface ReadIDCardRequest {
  ConnectType: ConnectType;
  ConnectStr: string;
  FunctionCode: CommandCode;
}

/**
 * 读取身份证信息接口返回
 */
export interface ReadIDCardResponse extends BaseResponse {
  /** 连接状态（如 "COM5"） */
  ConnectType: ConnectType;
  /** SAM码 */
  SAM: null;
  /** 身份证ID */
  UserID: null;
}

/**
 * 连续读取身份证信息接口返回
 */
export type ContinuityReadIDCardResponse = ReadIDCardResponse;

/**
 * 读取身份证指纹接口返回
 */
export type ReadFingerprintResponse = ReadIDCardResponse;

/**
 * 开/关天线接口返回
 */
export interface AntennaResponse {
  /** 命令类型 */
  CommandType: CommandCode;
  /** 操作结果（"True"） */
  Result: "True" | "False";
}

/**
 * 读取IC卡数据接口请求
 */
export interface ICReadRequest extends ReadRequest {
  /** 命令码 */
  FunctionCode: CommandCode;
  /** 扇区 */
  Sector: string;
  /** 密码 */
  Password: string;
  /** 写入数据 */
  Writedata: string;
  /** 块 */
  Block: string;
  /** A密钥 */
  ICKey: string;
}

/**
 * 读取身份证UID接口返回
 */
export interface ReadUserIDResponse extends BaseResponse<null> {
  /** 连接状态（如 "COM5"） */
  ConnectType: ConnectType;
  /** 身份证ID */
  UserID: string;
}

/**
 * 读取IC卡号、核验IC卡密码、读IC卡数据接口返回
 */
export interface ICCardResponse {
  /** 连接方式（如 "COM5"） */
  ConnectType: ConnectType;
  /** 扇区号 */
  Sector: string;
  /** IC卡号 */
  Number?: string;
  /** 块数据（读数据时有值） */
  Block?: string | null;
  /** 块类型 */
  BlockType?: string | null;
  /** 扇区密码 */
  SectorPassword?: string | null;
  /** 密码 */
  Password: string;
  /** 操作结果 */
  Result: boolean | string;
  /** 异常消息 */
  ErrorString: string | null;
}

/**
 * 写入IC卡数据接口返回
 */
export type WriteICCardResponse = ICCardResponse;
