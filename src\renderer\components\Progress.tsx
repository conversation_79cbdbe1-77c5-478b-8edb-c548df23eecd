import { motion } from "motion/react";
import type React from "react";
import { twMerge } from "tailwind-merge";

interface ProgressBarProps {
  /** 进度值 (0-100) */
  progress: number;
  /** 进度条宽度 */
  width?: string;
  /** 进度条高度 */
  height?: string;
  /** 背景颜色 */
  backgroundColor?: string;
  /** 进度条颜色 */
  progressColor?: string;
  /** 是否显示光晕效果 */
  showGlow?: boolean;
  /** 是否启用动画 */
  animated?: boolean;
  /** 自定义样式类 */
  className?: string;
  /** 进度条容器样式类 */
  containerClassName?: string;
}

/**
 * 进度条组件
 * 支持光晕效果和动画
 */
const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  width = "20.6vw",
  height = "0.94vw",
  backgroundColor = "#022653",
  progressColor = "linear-gradient(91deg, #0048ff 0%, #00a2ff 66%, #6fcaff 99%)",
  showGlow = true,
  animated = true,
  className = "",
  containerClassName = "",
}) => {
  // 确保进度值在0-100范围内
  const clampedProgress = Math.max(0, Math.min(100, progress));
  const progressWidth = `${(clampedProgress / 100) * 100}%`;

  return (
    <div className={twMerge("relative", containerClassName)}>
      {/* 主进度条容器 */}
      <div
        className={twMerge("relative rounded-xl ", className)}
        style={{
          width,
          height,
          backgroundColor,
        }}
      >
        {/* 进度条主体 */}

        <div className="absolute top-0 left-0 w-full h-full rounded-[1vw] overflow-hidden">
          <motion.div
            className="absolute top-0 left-0 h-full overflow-hidden rounded-[1vw]"
            style={{
              width: progressWidth,
              background: progressColor,
            }}
            initial={animated ? { width: "0%" } : false}
            animate={animated ? { width: progressWidth } : false}
            transition={{
              duration: 0.8,
              ease: "easeOut",
            }}
          />
        </div>

        {/* 进度条周围的光晕效果 */}
        {showGlow && (
          <motion.div
            className="absolute top-0 left-0 h-full rounded-xl"
            style={{
              width: progressWidth,
              background: progressColor,
              filter: "blur(0.3vw)",
              opacity: 0.6,
              transform: "scale(1.05)",
            }}
            initial={animated ? { width: "0%" } : false}
            animate={animated ? { width: progressWidth } : false}
            transition={{
              duration: 0.8,
              ease: "easeOut",
            }}
          />
        )}

        {/* 进度条边缘的发光效果 */}
        {showGlow && (
          <motion.div
            className="absolute top-0 left-0 h-full rounded-[1vw]"
            style={{
              width: progressWidth,
              background:
                "linear-gradient(90deg, rgba(0, 72, 255, 0.8), rgba(0, 162, 255, 0.6), rgba(111, 202, 255, 0.8))",
              filter: "blur(0.15vw)",
              opacity: 0.8,
            }}
            initial={animated ? { width: "0%" } : false}
            animate={animated ? { width: progressWidth } : false}
            transition={{
              duration: 0.8,
              ease: "easeOut",
            }}
          />
        )}

        {/* 进度条顶部的高光效果 */}
        {showGlow && (
          <motion.div
            className="absolute top-0 left-0 h-full rounded-xl"
            style={{
              width: progressWidth,
              background:
                "linear-gradient(180deg, rgba(255,255,255,0.2) 0%, transparent 50%)",
              filter: "blur(0.05vw)",
            }}
            initial={animated ? { width: "0%" } : false}
            animate={animated ? { width: progressWidth } : false}
            transition={{
              duration: 0.8,
              ease: "easeOut",
            }}
          />
        )}
      </div>
    </div>
  );
};

export default ProgressBar;
