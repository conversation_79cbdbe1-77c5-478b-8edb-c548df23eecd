import type { LibreOfficeAPI } from "./module/libreOffice/types";
import type { OcxAPI } from "./module/ocx/types";
import type { UsbAPI } from "./module/usb/types";
import type { UtilAPI } from "./module/util/types";
import type { WindowAPI } from "./module/window/types";

// 声明全局的 electronAPI 对象
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

/**
 * 主进程API接口
 * 统一暴露给渲染进程的API
 */
export interface ElectronAPI {
  /** 窗口控制 */
  window: WindowAPI;
  /** USB操作 */
  usb: UsbAPI;
  /** 工具 */
  util: UtilAPI;
  /** LibreOffice操作 */
  libreOffice: LibreOfficeAPI;
  /** 高拍仪 */
  ocx: OcxAPI;
}

export {};
