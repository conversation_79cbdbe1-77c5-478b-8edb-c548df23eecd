import type { FileInfo } from "@/types";
import { createSelector, createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "../..";
import type { FilesState } from "./types";

const initialState: FilesState = {
  uploadType: "camera",
  items: [],
  fileTextMap: [],
};

const filesSlice = createSlice({
  name: "files",
  initialState,
  reducers: {
    // 设置上传方式
    updateUploadType: (state, action: PayloadAction<"camera" | "usb">) => {
      state.uploadType = action.payload;
    },

    // 重新排序文件列表
    reorderFiles: (
      state,
      action: PayloadAction<{ fromIndex: number; toIndex: number }>
    ) => {
      const { fromIndex, toIndex } = action.payload;
      const [movedItem] = state.items.splice(fromIndex, 1);
      state.items.splice(toIndex, 0, movedItem);
    },

    // 添加文件
    addFile: (state, action: PayloadAction<FileInfo>) => {
      state.items.push(action.payload);
    },

    // 删除文件
    removeFile: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
    },

    // 设置文件列表
    setFiles: (state, action: PayloadAction<FileInfo[]>) => {
      state.items = action.payload;
    },

    // 设置文件加载状态
    setFileTextLoading: (
      state,
      action: PayloadAction<{ id: string; loading: boolean }>
    ) => {
      const { id, loading } = action.payload;
      const index = state.fileTextMap.findIndex(item => item.id === id);
      if (index !== -1) {
        state.fileTextMap[index].loading = loading;
      }
    },

    /**
     * 设置文件文本
     */
    updateFileText: (
      state,
      action: PayloadAction<{ id: string; text?: string; selected: boolean }>
    ) => {
      const { id, text, selected } = action.payload;
      const index = state.fileTextMap.findIndex(item => item.id === id);
      if (index !== -1) {
        state.fileTextMap[index] = {
          id,
          text: text || state.fileTextMap[index].text,
          selected: selected,
          loading: false,
        };
      } else {
        state.fileTextMap.push({
          id,
          text: text || "",
          selected: true,
          loading: false,
        });
      }
    },
  },
});

export const {
  updateUploadType,
  reorderFiles,
  addFile,
  removeFile,
  setFiles,
  setFileTextLoading,
  updateFileText,
} = filesSlice.actions;

/**
 * 融合文件列表和文件文本列表
 */
export const combineFileText = createSelector(
  [
    (state: RootState) => state.files?.fileTextMap,
    (state: RootState) => state.files?.items,
  ],
  (fileTextMap, items) => {
    return items?.map(item => {
      const text = fileTextMap?.find(file => file.id === item.id);
      return {
        ...text,
        file: item,
      };
    });
  }
);

/**
 * 是否存在正在加载的文件
 */
export const hasLoading = createSelector([combineFileText], fileText =>
  fileText?.some(item => item.loading)
);

export default filesSlice.reducer;
