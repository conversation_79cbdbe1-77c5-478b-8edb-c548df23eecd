import Button from "@/renderer/components/Button/BlueButton";
import { useEmptyPrint } from "@/renderer/hooks";
import { useAppSelector } from "@/renderer/store/hooks";
import key0 from "@assets/key/0.png";
import key1 from "@assets/key/1.png";
import key2 from "@assets/key/2.png";
import key3 from "@assets/key/3.png";
import key4 from "@assets/key/4.png";
import key5 from "@assets/key/5.png";
import key6 from "@assets/key/6.png";
import key7 from "@assets/key/7.png";
import key8 from "@assets/key/8.png";
import key9 from "@assets/key/9.png";
import keyBack from "@assets/key/back.png";
import { motion } from "framer-motion";
import { useState } from "react";
import { twMerge } from "tailwind-merge";

// prettier-ignore
const key = [
  key1,  key2,  key3,
  key4,  key5,  key6,
  key7,  key8,  key9,
  key0,         keyBack,
];
interface Props {
  /** 返回首页 */
  onBackHome?: () => void;
}
const PasswordEnter: React.FC<Props> = ({ onBackHome }) => {
  const userPassword = useAppSelector(state => state.user?.password);
  const [password, setPassword] = useState("");
  const [error, setError] = useState(""); // 新增错误提示状态
  const { emptyPrint } = useEmptyPrint();

  const handleConfirm = () => {
    if (password === userPassword) {
      setPassword("");
      setError(""); // 清除错误
      emptyPrint();
      onBackHome?.();
    } else {
      if (password.length !== 0) {
        setError("密码错误，请重试");
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      transition={{ duration: 0.3 }}
      className="px-[3vw] absolute w-full top-[6.5vw] bottom-[7.45vw] flex items-center justify-between gap-[4vw]"
    >
      <div className="w-[50%] h-[100%] flex flex-col ">
        <div className="text-[1.25vw] mt-[20%]  leading-none text-gradient">
          请在此输入打印密码
        </div>
        <div
          className="w-[100%] h-[3vw] mt-[1vw] border border-[#2155a0]"
          style={{
            background: `
              linear-gradient(to right, transparent 95%, rgba(33, 85, 160, 0.5) 100%),
              linear-gradient(to left, transparent 95%, rgba(33, 85, 160, 0.5) 100%),
              linear-gradient(to bottom, transparent 50%, rgba(33, 85, 160, 0.5) 100%),
              linear-gradient(to top, transparent 50%, rgba(33, 85, 160, 0.5) 100%)
            `,
          }}
        >
          <input
            type="password"
            className="w-full h-[3vw] outline-none bg-transparent px-[1vw] text-white"
            value={password}
            onChange={e => {
              setPassword(e.target.value);
              setError(""); // 输入时清除错误提示
            }}
          />
        </div>
        {/* 新增错误提示 */}
        {/* {error && ( */}
        <div className="text-red-500 text-[1vw] mt-[0.5vw] h-[3vw]">
          {error}
        </div>
        {/* )} */}
        <div className="flex justify-center text-white">
          <Button onClick={handleConfirm}>确认打印</Button>
        </div>
      </div>
      {/* 键盘 */}
      <div className="w-[50%] h-[100%]">
        <div className="grid grid-cols-3 gap-x-[2vw] gap-y-[1vw]">
          {key.map((item, index) => (
            <motion.div
              key={index}
              className={twMerge(
                "w-[100%] h-[4vw] cursor-pointer",
                index === 9 && "col-span-2"
              )}
              style={{
                background: `url(${item}) no-repeat center center`,
                backgroundSize: "contain",
              }}
              whileTap={{ scale: 0.9 }}
              onClick={() => {
                if (index < 9) {
                  setPassword(prev => prev + (index + 1));
                } else if (index === 9) {
                  setPassword(prev => prev + 0);
                } else {
                  setPassword(prev => prev.slice(0, -1));
                }
              }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default PasswordEnter;
