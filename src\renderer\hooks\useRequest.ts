import httpRequest from "@/renderer/utils/request";
import { useCallback, useEffect, useRef, useState } from "react";
import { RequestConfig, RequestStatus } from "../utils/request/types";

/**
 * 请求状态接口
 */
interface RequestState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  status: RequestStatus;
}

/**
 * 请求配置接口
 */
interface UseRequestConfig<T> extends RequestConfig {
  // 是否立即执行请求
  immediate?: boolean;
  // 请求完成后的回调
  onSuccess?: (data: T) => void;
  // 请求失败后的回调
  onError?: (error: Error) => void;
  // 请求完成后的回调（无论成功或失败）
  onFinally?: () => void;
  // 依赖项数组，当依赖项改变时重新请求
  deps?: React.DependencyList;
}

/**
 * 请求Hook返回值接口
 */
interface UseRequestReturn<T> {
  // 请求状态
  data: T | null;
  loading: boolean;
  error: Error | null;
  status: RequestStatus;

  run: (config?: RequestConfig) => Promise<T>;
  refresh: () => Promise<T>;
  cancel: () => void;

  get: (url: string, config?: RequestConfig) => Promise<T>;
  post: (url: string, data?: any, config?: RequestConfig) => Promise<T>;
  put: (url: string, data?: any, config?: RequestConfig) => Promise<T>;
  delete: (url: string, config?: RequestConfig) => Promise<T>;
}

/**
 * 网络请求Hook
 */
export function useRequest<T = any>(
  requestConfig?: RequestConfig,
  options: UseRequestConfig<T> = {}
): UseRequestReturn<T> {
  const {
    immediate = false,
    onSuccess,
    onError,
    onFinally,
    deps = [],
    ...restOptions
  } = options;

  // 请求状态
  const [state, setState] = useState<RequestState<T>>({
    data: null,
    loading: false,
    error: null,
    status: RequestStatus.IDLE,
  });

  // 请求配置引用
  const configRef = useRef<RequestConfig | undefined>(requestConfig);
  const optionsRef = useRef<UseRequestConfig<T>>(restOptions);

  // 更新引用
  configRef.current = requestConfig;
  optionsRef.current = restOptions;

  // 请求ID引用
  const requestIdRef = useRef<string | null>(null);

  // 更新状态
  const updateState = useCallback((partialState: Partial<RequestState<T>>) => {
    setState(prevState => ({
      ...prevState,
      ...partialState,
    }));
  }, []);

  // 通用请求方法
  const executeRequest = useCallback(
    async (config: RequestConfig): Promise<T> => {
      // 生成请求ID
      const requestId = `hook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      requestIdRef.current = requestId;

      // 更新状态
      updateState({
        loading: true,
        error: null,
        status: RequestStatus.LOADING,
      });

      try {
        // 执行请求
        const result = await httpRequest.request<T>({
          ...optionsRef.current,
          ...config,
          requestId,
        });

        // 检查请求是否已被取消
        if (requestIdRef.current === requestId) {
          updateState({
            data: result,
            loading: false,
            status: RequestStatus.SUCCESS,
          });

          // 执行成功回调
          if (onSuccess) {
            onSuccess(result);
          }

          return result;
        }

        return result;
      } catch (error) {
        // 检查请求是否已被取消
        if (requestIdRef.current === requestId) {
          const errorObj = error as Error;
          updateState({
            error: errorObj,
            loading: false,
            status: RequestStatus.ERROR,
          });

          // 执行错误回调
          if (onError) {
            onError(errorObj);
          }
        }

        throw error;
      } finally {
        // 检查请求是否已被取消
        if (requestIdRef.current === requestId) {
          // 执行完成回调
          if (onFinally) {
            onFinally();
          }
        }
      }
    },
    [onSuccess, onError, onFinally, updateState]
  );

  // 执行请求
  const run = useCallback(
    async (config?: RequestConfig): Promise<T> => {
      const finalConfig = { ...configRef.current, ...config };
      return executeRequest(finalConfig);
    },
    [executeRequest]
  );

  // 刷新请求
  const refresh = useCallback(async (): Promise<T> => {
    if (!configRef.current) {
      throw new Error("No request configuration available");
    }
    return executeRequest(configRef.current);
  }, [executeRequest]);

  // 取消请求
  const cancel = useCallback(() => {
    if (requestIdRef.current) {
      httpRequest.cancelRequest(requestIdRef.current);
      requestIdRef.current = null;
      updateState({
        loading: false,
        status: RequestStatus.IDLE,
      });
    }
  }, [updateState]);

  // 便捷方法
  const get = useCallback(
    (url: string, config?: RequestConfig): Promise<T> => {
      return run({ ...config, method: "GET", url });
    },
    [run]
  );

  const post = useCallback(
    (url: string, data?: any, config?: RequestConfig): Promise<T> => {
      return run({ ...config, method: "POST", url, data });
    },
    [run]
  );

  const put = useCallback(
    (url: string, data?: any, config?: RequestConfig): Promise<T> => {
      return run({ ...config, method: "PUT", url, data });
    },
    [run]
  );

  const deleteMethod = useCallback(
    (url: string, config?: RequestConfig): Promise<T> => {
      return run({ ...config, method: "DELETE", url });
    },
    [run]
  );

  // 立即执行请求
  useEffect(() => {
    if (immediate && configRef.current) {
      run();
    }
  }, [immediate, run]);

  // 依赖项变化时重新请求
  useEffect(() => {
    if (deps.length > 0 && configRef.current) {
      run();
    }
    // eslint-disable-next-line
  }, deps);

  // 组件卸载时取消请求
  useEffect(() => {
    return () => {
      cancel();
    };
  }, [cancel]);

  return {
    // 状态
    data: state.data,
    loading: state.loading,
    error: state.error,
    status: state.status,

    // 操作方法
    run,
    refresh,
    cancel,

    // 便捷方法
    get,
    post,
    put,
    delete: deleteMethod,
  };
}

/**
 * 简化的请求Hook（只用于GET请求）
 */
export function useGet<T = any>(
  url: string,
  config?: RequestConfig,
  options: UseRequestConfig<T> = {}
): UseRequestReturn<T> {
  return useRequest<T>(
    { method: "GET", url, ...config },
    { immediate: true, ...options }
  );
}

/**
 * 延迟请求Hook（不立即执行）
 */
export function useLazyRequest<T = any>(
  requestConfig?: RequestConfig,
  options: UseRequestConfig<T> = {}
): UseRequestReturn<T> {
  return useRequest<T>(requestConfig, { immediate: false, ...options });
}

/**
 * 分页请求Hook
 */
export function usePagination<T = any>(
  url: string,
  config?: RequestConfig,
  options: Omit<
    UseRequestConfig<{ list: T[]; total: number; page: number; size: number }>,
    "onSuccess"
  > & {
    onSuccess?: (data: {
      list: T[];
      total: number;
      page: number;
      size: number;
    }) => void;
  } = {}
) {
  const [pagination, setPagination] = useState({
    page: 1,
    size: 10,
    total: 0,
  });

  const requestHook = useRequest<{
    list: T[];
    total: number;
    page: number;
    size: number;
  }>(
    {
      method: "GET",
      url,
      params: {
        page: pagination.page,
        size: pagination.size,
      },
      ...config,
    },
    {
      immediate: true,
      onSuccess: data => {
        setPagination(prev => ({
          ...prev,
          total: data.total,
          page: data.page,
          size: data.size,
        }));
        if (options.onSuccess) {
          options.onSuccess(data);
        }
      },
      ...options,
    }
  );

  const changePage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  const changeSize = useCallback((size: number) => {
    setPagination(prev => ({ ...prev, size, page: 1 }));
  }, []);

  return {
    ...requestHook,
    pagination,
    changePage,
    changeSize,
  };
}
