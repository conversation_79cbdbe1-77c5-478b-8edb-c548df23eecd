import type { FileRenderProps } from "./types";

const ImagePreview: React.FC<FileRenderProps> = ({ filePath, fileName }) => {
  return (
    <div className="w-full h-full flex items-center justify-center bg-white rounded-lg overflow-hidden">
      <img
        src={filePath}
        alt={fileName}
        className="max-w-full max-h-full object-contain"
      />
    </div>
  );
};

export default ImagePreview;
