import type { FileInfo } from "@/types/services";
import type {
  OCXCommand,
  OCXMode,
  OCXVideoDispMode,
} from "@/types/services/ocxService";

/**
 * todo:优化 后续移除该文件，规范（channel，回调）
 */
export interface OcxAPI {
  /**
   * 开始拍摄
   * @returns 返回是否成功
   */
  startPlay: () => Promise<boolean>;
  /**
   * 停止拍摄
   * @returns 返回是否成功
   */
  stopPlay: () => Promise<void>;
  /**
   * 设置拍摄模式
   * @param mode 拍摄模式
   * @returns 返回是否成功
   */
  setMode: (mode: OCXMode) => Promise<void>;
  setVideoDispMode: (mode: OCXVideoDispMode) => Promise<void>;
  /**
   * 拍照
   * @param type 拍照类型
   * @returns 返回文件信息
   */
  takePhoto: (type: "JPG" | "PNG" | "TifJPG") => Promise<FileInfo | null>;
  /**
   * 清空文件夹
   * @returns 返回是否成功
   */
  clearFolder: () => Promise<boolean>;
  /**
   * 监听图片流
   * @param callback 回调函数
   */
  onImageStream: (
    callback: (data: { base64: boolean; data: string }) => void
  ) => void;
  /**
   * 任意命令
   * @param data 数据
   * @returns 返回是否成功
   */
  anyCommand: (data: OCXCommand) => Promise<void>;
}
