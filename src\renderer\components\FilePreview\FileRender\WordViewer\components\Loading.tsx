import LoadingSpinner from "@/renderer/components/LoadingSpinner";
import { AnimatePresence, motion } from "motion/react";

interface Props {
  visible: boolean;
}

const Loading: React.FC<Props> = ({ visible }) => {
  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          className="absolute inset-0 flex items-center justify-center z-10 w-full h-full"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <LoadingSpinner text="正在转换文档..." className="w-full h-full" />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Loading;
