import type {
  DisputeFormv2Detail,
  Passage,
} from "@/renderer/service/dispute/types";
import { getFileType } from "@/renderer/utils/file";
import type { FileInfo } from "@/types";
import { DisputePageType, DisputeType } from "@/types/views/dispute";
import {
  createSelector,
  createSlice,
  type PayloadAction,
} from "@reduxjs/toolkit";
import type { RootState } from "../..";
import type { ClientState } from "./types";

const client: ClientState = {
  mid: 801,
  tid: "",
  isAnalyzing: false,
  disputeType: undefined,
  disputePageType: DisputePageType.LAWSUIT,
  passage: null,
  enterType: "empty",
  activeDispute: [],
  isPrint: false,
};

const clientSlice = createSlice({
  name: "client",
  initialState: client,
  reducers: {
    /** 设置设备ID */
    setMid: (state, action: PayloadAction<number>) => {
      state.mid = action.payload;
    },
    /** 设置扫码ID */
    setTid: (state, action: PayloadAction<string>) => {
      state.tid = action.payload;
    },
    /** 设置纠纷类型 */
    setDisputeType: (state, action: PayloadAction<DisputeType>) => {
      state.disputeType = action.payload;
    },
    /** 设置纠纷表单类型 */
    setDisputePageType: (state, action: PayloadAction<DisputePageType>) => {
      state.disputePageType = action.payload;
    },
    /** 点击开始分析按钮 */
    setIsAnalyzing: (state, action: PayloadAction<boolean>) => {
      state.isAnalyzing = action.payload;
    },
    /** 清空纠纷类型和表单类型 */
    clearDisputeType: state => {
      state.disputeType = undefined;
      state.disputePageType = DisputePageType.LAWSUIT;
    },

    /** 设置提取要素表的段落 */
    setPassage: (state, action: PayloadAction<ClientState["passage"]>) => {
      state.passage = action.payload;
    },

    /** 开始提取要素表的段落 */
    startFetchingPassage(state, action: PayloadAction<Passage[]>) {
      if (!state.passage) {
        state.passage = {};
      }
      for (const type of action.payload) {
        state.passage[type] = { text: null, status: "loading" };
      }
    },

    /** 更新提取要素表的段落 */
    updatePassage: (
      state,
      action: PayloadAction<Partial<ClientState["passage"]>>
    ) => {
      state.passage = {
        ...state.passage,
        ...action.payload,
      };
    },

    /** 设置当前选中的案由 */
    setEnterType: (state, action: PayloadAction<ClientState["enterType"]>) => {
      state.enterType = action.payload;
    },

    /** 设置当前选中的案由 */
    setIsPrint: (state, action: PayloadAction<ClientState["isPrint"]>) => {
      state.isPrint = action.payload;
    },

    /** 设置当前选中的案由 */
    setActiveDispute: (state, action: PayloadAction<DisputeFormv2Detail[]>) => {
      state.activeDispute = action.payload;
    },

    /** 清空提取要素表的段落 */
    clearPassage: state => {
      state.passage = null;
    },

    /** 还原client状态 */
    restore: () => client,
  },
});

export const pickPageFiles = createSelector(
  [
    (state: RootState) => state.client?.disputePageType,
    (state: RootState) => state.client?.activeDispute,
  ],
  (type, active): { files: FileInfo[]; id: number } | null => {
    const exist = active?.find(item => item.type_alias === type);

    if (exist) {
      return {
        id: exist.id,
        files: exist.file.map(item => {
          const fileName = item.path.split("/").pop();

          return {
            ...item,
            id: item.id.toString(),
            name: fileName || item.path,
            remote: true,
            type: getFileType(item.path),
          };
        }),
      };
    }

    return null;
  }
);

export const {
  setMid,
  setTid,
  setDisputeType,
  setDisputePageType,
  clearDisputeType,
  setIsAnalyzing,
  setPassage,
  startFetchingPassage,
  updatePassage,
  setEnterType,
  setIsPrint,
  setActiveDispute,
  clearPassage,
  restore,
} = clientSlice.actions;

export default clientSlice.reducer;
