import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import { setFiles, updateUploadType } from "../store/slice/file";
import { clearSelection } from "../store/slice/usb/thunks";

/**
 * 文件操作
 * @returns 返回文件操作
 */
const useFilesAction = () => {
  const dispatch = useAppDispatch();
  const uploadType = useAppSelector(state => state.files?.uploadType);

  /**
   * 清空文件夹
   */
  const clearFolder = useCallback(() => {
    if (uploadType === "camera") {
      window.electronAPI.ocx.clearFolder();
    } else if (uploadType === "usb") {
      dispatch(clearSelection());
    }
    dispatch(updateUploadType("camera"));
    dispatch(setFiles([]));
  }, [dispatch, uploadType]);

  return {
    uploadType,

    clearFolder,
  };
};

export default useFilesAction;
