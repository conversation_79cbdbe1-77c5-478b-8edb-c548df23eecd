import { protocol } from "electron";

/**
 * 注册协议
 */
const registerProtocols = () => {
  protocol.registerSchemesAsPrivileged([
    {
      scheme: "usb",
      privileges: {
        // 允许使用标准协议
        standard: true,
        // 允许跨域请求
        secure: true,
        // 允许使用fetch API
        supportFetchAPI: true,
        // 允许CORS请求
        corsEnabled: true,
      },
    },
    {
      scheme: "localFile",
      privileges: {
        // 允许使用标准协议
        standard: true,
        // 允许跨域请求
        secure: true,
        // 允许使用fetch API
        supportFetchAPI: true,
        // 允许CORS请求
        corsEnabled: true,
      },
    },
  ]);
};

export default registerProtocols;
