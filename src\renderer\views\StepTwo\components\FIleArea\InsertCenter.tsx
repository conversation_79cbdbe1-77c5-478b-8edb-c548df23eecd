import { useAppSelector } from "@/renderer/store/hooks";
import cameraTips from "@assets/step-two/camera-tips.png";
import usbTips from "@assets/step-two/usb-tips.png";

const InsertCenter: React.FC = () => {
  const uploadType = useAppSelector(state => state.files?.uploadType);
  return (
    <div className="flex justify-center items-center flex-1">
      <img
        src={uploadType === "usb" ? usbTips : cameraTips}
        alt=""
        className="h-[13.33vw] object-contain"
      />
    </div>
  );
};

export default InsertCenter;
