import { useDispute } from "@/renderer/hooks";
import React, { useEffect } from "react";
import { useAppSelector } from "../store/hooks";

interface DataPreloaderProps {
  /** 子组件 */
  children: React.ReactNode;
  /** 是否预加载纠纷数据 */
  preloadDispute?: boolean;
}

/**
 * 数据预加载组件
 */
const DataPreloader: React.FC<DataPreloaderProps> = ({
  children,
  preloadDispute = true,
}) => {
  const mid = useAppSelector(state => state.client?.mid);
  // 预加载纠纷数据
  const { fetchForms, initialized } = useDispute();

  useEffect(() => {
    if (preloadDispute && !initialized && mid) {
      fetchForms({ mid, limit: 12 });
    }
  }, [preloadDispute, initialized, fetchForms, mid]);

  return <>{children}</>;
};

export default DataPreloader;
