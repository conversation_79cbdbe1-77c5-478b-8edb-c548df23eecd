/**
 * LibreOffice 转换结果
 */
export interface LibreOfficeResult {
  /** 是否成功 */
  success: boolean;
  /** 输出文件路径 */
  outputPath?: string;
  /** 缩略图路径 */
  thumbnailPath?: string;
  /** 错误信息 */
  error?: string;
  /** 转换耗时（毫秒） */
  duration?: number;
}

/**
 * LibreOffice API 接口
 */
export interface LibreOfficeAPI {
  /** 转换文档为 PDF */
  convertToPdf: (filePath: string) => Promise<LibreOfficeResult>;
  /** 转换文档为图片 */
  convertToImage: (
    filePath: string,
    format?: "png" | "jpg"
  ) => Promise<LibreOfficeResult>;
  /** 生成文档缩略图 */
  generateThumbnail: (filePath: string) => Promise<LibreOfficeResult>;
  /** 检查 LibreOffice 是否可用 */
  checkAvailability: () => Promise<boolean>;
  // getInfo: () => Promise<LibreOfficeInfo>; // 新增
}
