import { addPublicPrefix } from "@/main/utils/path";
import { useDragSort, useUsb } from "@/renderer/hooks";
import { useAppDispatch } from "@/renderer/store/hooks";
import { removeFile } from "@/renderer/store/slice/file";
import { getFileCover } from "@/renderer/utils/file";
import type { UsbFileInfo } from "@/types";
import { closestCenter, DndContext, DragOverlay } from "@dnd-kit/core";
import { rectSortingStrategy, SortableContext } from "@dnd-kit/sortable";
import { useState } from "react";
import DeleteConfim from "../DeleteConfim";
import FileCard from "./FileCard";

interface Props {
  /**
   * 双击预览回调
   * @param file 文件信息
   */
  onPreview?: (file: UsbFileInfo, index: number) => void;
}

const FileDrag: React.FC<Props> = ({ onPreview }) => {
  const {
    files,
    activeFile,
    sensors,
    handleDragStart,
    handleDragEnd,
    getActiveFileIndex,
    sortableItems,
  } = useDragSort();
  const { toggleFileSelection } = useUsb();

  const dispatch = useAppDispatch();
  const [deleteId, setDeleteId] = useState<string | null>(null);

  const handleDelete = () => {
    if (!deleteId) return;
    dispatch(removeFile(deleteId));
    toggleFileSelection(deleteId, false);
    setDeleteId(null);
  };

  return (
    <div className="flex-1 overflow-y-auto custom-scrollbar">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={sortableItems} strategy={rectSortingStrategy}>
          <div className="flex flex-wrap gap-[.95vw]">
            {files.map((file, index) => (
              <FileCard
                key={file.id}
                file={file}
                index={index}
                onDelete={setDeleteId}
                onPreview={onPreview}
              />
            ))}
          </div>
        </SortableContext>

        {/* 拖拽预览层 */}
        <DragOverlay>
          {activeFile ? (
            <div className="w-[10.5vw] overflow-hidden opacity-90">
              <div className="relative w-[10.5vw] h-[14.56vw] bg-white rounded-[.6vw] border-[.3vw] border-[#4686df] overflow-hidden shadow-2xl">
                <img
                  src={getFileCover(
                    activeFile.type,
                    addPublicPrefix(activeFile.path, {
                      remote: activeFile.remote,
                    })
                  )}
                  alt={activeFile.name}
                  className="h-full object-contain p-3"
                  draggable={false}
                />
                <div className="absolute bottom-0 left-0 w-[2.4vw] h-[2.08vw] flex items-center justify-center bg-black/50 rounded-se-[.42vw] text-[1.3vw] text-white font-bold italic">
                  {getActiveFileIndex()}
                </div>
              </div>
              <div className="text-center text-[#769dda] truncate">
                {activeFile.name}
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
      {/* 删除确认 */}
      <DeleteConfim
        visible={!!deleteId}
        onDelete={handleDelete}
        onCancel={() => setDeleteId(null)}
      />
    </div>
  );
};

export default FileDrag;
