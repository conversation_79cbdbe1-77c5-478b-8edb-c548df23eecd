import type React from "react";
import { memo } from "react";
import { twMerge } from "tailwind-merge";

type Props = {
  className?: string;
  children: React.ReactNode;
};

/**
 * 内容区域
 */
const Content: React.FC<Props> = ({ children, className }) => {
  return (
    <div
      className={twMerge(
        "absolute w-full top-[6.5vw] bottom-[7.45vw] flex items-center justify-between px-[10vw] gap-[4vw]",
        className
      )}
    >
      {children}
    </div>
  );
};

export default memo(Content);
