// BYYJWebSocket.ts
import type {
  AntennaResponse,
  ConnectType,
  ContinuityReadIDCardResponse,
  ICCardResponse,
  ICReadRequest,
  ReadFingerprintResponse,
  ReadIDCardResponse,
  ReadRequest,
  ReadSAMResponse,
  ReadUserIDResponse,
  WriteICCardResponse,
} from "@/renderer/devices/card/types";

/**
 * BYYJWebSocket 身份证/IC卡 WebSocket 通信服务
 */
export class CardService {
  private ws: WebSocket | null = null;
  private url: string;
  private isReady = false;
  private queue: Array<{
    resolve: (data: any) => void;
    reject: (err: any) => void;
  }> = [];

  constructor(url = "ws://localhost:8091") {
    this.url = url;
  }

  private onMessageHandlers: ((data: any) => void)[] = [];

  /**
   * 连接 WebSocket
   */
  private connect(): Promise<void> {
    if (this.ws && this.isReady) return Promise.resolve();
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.url);
      this.ws.onopen = () => {
        this.isReady = true;
        resolve();
      };
      this.ws.onerror = err => {
        this.isReady = false;
        reject(err);
      };
      this.ws.onclose = () => {
        this.isReady = false;
      };
      this.ws.onmessage = event => {
        try {
          const data = JSON.parse(event.data);

          // 有监听器就全部触发
          if (this.onMessageHandlers.length > 0) {
            this.onMessageHandlers.forEach(handler => handler(data));
            return;
          }

          // 分发请求
          if (this.queue.length > 0) {
            const item = this.queue.shift();
            item?.resolve(data);
          }
        } catch (e) {
          // todo: 解析失败
        }
      };
    });
  }

  /**
   * 断开 WebSocket
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.isReady = false;
    }
  }

  /**
   * 发送请求
   */
  private async sendRequest<T = any>(req: ReadRequest): Promise<T> {
    await this.connect();
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN)
      throw new Error("WebSocket not connected");
    return new Promise((resolve, reject) => {
      this.queue.push({ resolve, reject });
      this.ws?.send(JSON.stringify(req));
      // 超时处理
      setTimeout(() => {
        if (this.queue.length > 0) {
          const idx = this.queue.findIndex(item => item.resolve === resolve);
          if (idx !== -1) {
            this.queue.splice(idx, 1);
            reject("Timeout");
          }
        }
      }, 5000);
    });
  }

  /**
   * 读取 SAM 码（安全模块序列号）
   * @param connectType 连接类型（"COM" | "USB" | "NET"）
   * @param connectStr 端口号（如 "COM1"、"USB1" 等）
   * @returns
   */
  readSAM(connectType: ConnectType, connectStr: string) {
    return this.sendRequest<ReadSAMResponse>({
      ConnectType: connectType,
      ConnectStr: connectStr,
      FunctionCode: "boyayingjie_ReadSAM",
    });
  }

  /**
   * 读取身份证信息（方式一，单次读卡）
   * @param connectType 连接类型（"COM" | "USB" | "NET"）
   * @param connectStr 端口号（如 "COM1"、"USB1" 等）
   * @returns
   */
  readIDCard(connectType: ConnectType, connectStr: string) {
    return this.sendRequest<ReadIDCardResponse>({
      ConnectType: connectType,
      ConnectStr: connectStr,
      FunctionCode: "boyayingjie_Single",
    });
  }

  /**
   * 连续读取身份证信息（方式二，需配合 stopContinuousRead 使用）
   * @param connectType 连接类型（"COM" | "USB" | "NET"）
   * @param connectStr 端口号（如 "COM1"、"USB1" 等）
   * @returns
   */
  startContinuousRead(
    connectType: ConnectType,
    connectStr: string,
    listener: (data: ContinuityReadIDCardResponse) => void
  ) {
    this.onMessageHandlers.push(listener);
    this.sendRequest<ContinuityReadIDCardResponse>({
      ConnectType: connectType,
      ConnectStr: connectStr,
      FunctionCode: "boyayingjie_Continuity",
    });
  }

  /**
   * 取消连续读取身份证信息（方式二）
   * @param connectType 连接类型（"COM" | "USB" | "NET"）
   * @param connectStr 端口号（如 "COM1"、"USB1" 等）
   * @returns
   */
  stopContinuousRead(connectType: ConnectType, connectStr: string) {
    return this.sendRequest<any>({
      ConnectType: connectType,
      ConnectStr: connectStr,
      FunctionCode: "boyayingjie_Stop",
    });
  }

  /**
   * 读取身份证指纹
   * @param connectType 连接类型（"COM" | "USB" | "NET"）
   * @param connectStr 端口号（如 "COM1"、"USB1" 等）
   * @returns
   */
  readFingerprint(connectType: ConnectType, connectStr: string) {
    return this.sendRequest<ReadFingerprintResponse>({
      ConnectType: connectType,
      ConnectStr: connectStr,
      FunctionCode: "boyayingjie_Fingerprint",
    });
  }

  /**
   * 开天线（二合一设备专用）
   * @param connectType 连接类型（"COM" | "USB" | "NET"）
   * @param connectStr 端口号（如 "COM1"、"USB1" 等）
   * @returns Promise 解析为 AntennaResponse
   */
  openAntenna(connectType: ConnectType, connectStr: string) {
    return this.sendRequest<AntennaResponse>({
      ConnectType: connectType,
      ConnectStr: connectStr,
      FunctionCode: "boyayingjie_OpenAntenna",
    });
  }

  /**
   * 关天线（二合一设备专用）
   * @param connectType 连接类型（"COM" | "USB" | "NET"）
   * @param connectStr 端口号（如 "COM1"、"USB1" 等）
   * @returns
   */
  closeAntenna(connectType: ConnectType, connectStr: string) {
    return this.sendRequest<AntennaResponse>({
      ConnectType: connectType,
      ConnectStr: connectStr,
      FunctionCode: "boyayingjie_OFFAntenna",
    });
  }

  /**
   * 读取身份证 UID（二合一设备专用）
   * @param connectType 连接类型（"COM" | "USB" | "NET"）
   * @param connectStr 端口号（如 "COM1"、"USB1" 等）
   * @returns
   */
  readUserID(connectType: ConnectType, connectStr: string) {
    return this.sendRequest<ReadUserIDResponse>({
      ConnectType: connectType,
      ConnectStr: connectStr,
      FunctionCode: "boyayingjie_ReadUserID",
    });
  }

  /**
   * 读取 IC 卡号
   * @param params 参数对象
   *   - ConnectType: 连接类型（"COM" | "USB" | "NET"）
   *   - ConnectStr: 端口号（如 "COM1"）
   *   - Sector: 扇区号（如 "0"）
   *   - Password: IC 卡密码（如 "FFFFFFFFFFFF"）
   *   - Writedata: 写入数据（可选，读卡时可为空字符串）
   *   - Block: 块号（如 "0"）
   *   - ICKey: 密钥类型（如 "1" 表示 A 密钥）
   * @returns
   */
  readICNumber(params: ICReadRequest) {
    return this.sendRequest<ICCardResponse>({
      ...params,
      FunctionCode: "boyayingjie_ICNumber",
    });
  }

  /**
   * 核验 IC 卡密码
   * @param params
   * @returns
   */
  verifyICPassword(params: ICReadRequest) {
    return this.sendRequest<ICCardResponse>({
      ...params,
      FunctionCode: "boyayingjie_ICVerify",
    });
  }

  /**
   * 读取 IC 卡数据
   * @param params
   * @returns
   */
  readICData(params: ICReadRequest) {
    return this.sendRequest<ICCardResponse>({
      ...params,
      FunctionCode: "boyayingjie_ReadSector",
    });
  }

  /**
   * 写入 IC 卡数据
   * @param params
   * @returns
   */
  writeICData(params: ICReadRequest) {
    return this.sendRequest<WriteICCardResponse>({
      ...params,
      FunctionCode: "boyayingjie_WriteSector",
    });
  }
}
