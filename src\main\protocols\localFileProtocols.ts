import { protocol } from "electron";
import fs from "fs";
import mime from "mime";
import path from "path";

/**
 * 注册 本地文件 协议处理程序
 * 用于在渲染进程中访问 本地文件
 *
 * 使用方法：
 * 在渲染进程中，使用 `localfile://` 协议访问 本地文件
 * 例如：
 * const response = await fetch('localfile://path/to/file.txt');
 * const text = await response.text();
 * console.log(text);
 *
 * TODO: 注意：
 * 1. 本地文件协议处理程序需要注册在主进程中，不能在渲染进程中注册
 * 2. 目前除协议名外，其他均和USB一致，后续考虑合并
 *
 */
const registerLocalFileProtocolHandler = () => {
  try {
    protocol.handle("localfile", async request => {
      // 从 URL 中提取文件路径
      const url = new URL(request.url);
      // mac 和 linux 路径
      let filePath = decodeURIComponent(`/${url.host}${url.pathname}`);

      if (process.platform === "win32") {
        if (url.host && url.pathname) {
          const joinedPath = path.join(`${url.host}:\\`, url.pathname); // 使用 \\

          filePath = path.normalize(decodeURIComponent(joinedPath));
        } else {
          filePath = path.normalize(decodeURIComponent(url.pathname));
        }
      }

      if (!path.isAbsolute(filePath) || !fs.existsSync(filePath)) {
        return new Response("File not found", { status: 404 });
      }

      // 读取文件内容
      const fileBuffer = await fs.promises.readFile(filePath);

      // 获取文件的类型
      const ext = path.extname(filePath).toLowerCase();
      const mimeType = mime.getType(ext);

      return new Response(fileBuffer, {
        headers: {
          "Content-Type": mimeType || "application/octet-stream",
          "Content-Length": fileBuffer.length.toString(),
        },
      });
    });
  } catch (error) {
    console.error("Failed to read local files:", error);
    return new Response("Internal Server Error", {
      status: 500,
    });
  }
};

export { registerLocalFileProtocolHandler };
