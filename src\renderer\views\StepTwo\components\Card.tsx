import { removeFile } from "@/renderer/store/slice/file";
import type { UsbFileInfo } from "@/types";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type React from "react";
import { useAppDispatch } from "../../../store/hooks";

type Props = {
  file: UsbFileInfo;
  index: number;
};

const Card: React.FC<Props> = ({ file, index }) => {
  const dispatch = useAppDispatch();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: file.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(removeFile(file.id));
  };
  return (
    <div
      ref={setNodeRef}
      style={style}
      className="w-[10.5vw] overflow-hidden"
      {...attributes}
    >
      <div
        className={`relative w-[10.5vw] h-[14.56vw] bg-white rounded-[.6vw] border-[.3vw] border-[#4686df] overflow-hidden cursor-grab ${isDragging ? "cursor-grabbing" : ""}`}
        {...listeners}
      >
        {/* 关闭按钮 */}
        <div
          className="absolute top-0 right-0 w-[1.67vw] h-[1.67vw] bg-danger-default rounded-es-[.3vw] flex items-center justify-center cursor-pointer z-10"
          onClick={handleDelete}
        >
          <div className="w-[1.3vw] h-[.1vw] bg-white rounded-full rotate-45 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"></div>
          <div className="w-[1.3vw] h-[.1vw] bg-white rounded-full -rotate-45 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"></div>
        </div>

        <img src={file.path} alt={file.name} className="w-full h-full" />

        {/* 序号 */}
        <div className="absolute bottom-0 left-0 w-[2.4vw] h-[2.08vw] flex items-center justify-center bg-black/50 rounded-se-[.42vw] text-[1.3vw] text-white font-bold italic ">
          {index}
        </div>
      </div>
      <div className="text-center text-[#769dda] truncate">{file.name}</div>
    </div>
  );
};

export default Card;
