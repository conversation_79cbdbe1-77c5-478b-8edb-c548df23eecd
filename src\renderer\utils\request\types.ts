import { AxiosRequestConfig, AxiosResponse } from "axios";
import { STATUS_CODE } from "./config";

/**
 * 请求配置接口
 */
export interface RequestConfig extends AxiosRequestConfig {
  // 是否显示加载状态
  showLoading?: boolean;
  // 是否显示错误提示
  showError?: boolean;
  // 是否需要token认证
  needAuth?: boolean;
  // 重试次数
  retryCount?: number;
  // 请求标识（用于取消请求）
  requestId?: string;
  // 请求实例名称
  instanceName?: string;
}

export type ResponseCode = 1 | 1001 | 401 | 403 | 404 | 500 | 502 | 503 | 504;

export type ResponseInfo = {
  elapsed: number;
  memory: string;
  t1: number;
  t2: number;
};

/**
 * 响应数据接口
 */
export interface ResponseData<T = any> {
  code: STATUS_CODE;
  data: T;
  msg: string;
  elapsed: number;
  msg_id: string;
  info: ResponseInfo;
}

/**
 * 自定义响应接口
 */
export interface CustomResponse<T = any> extends AxiosResponse {
  data: ResponseData<T>;
}

/**
 * 错误响应接口
 */
export interface ErrorResponse {
  code: number;
  msg: string;
  details?: any;
}

/**
 * 请求状态枚举
 */
export enum RequestStatus {
  IDLE = "idle",
  LOADING = "loading",
  SUCCESS = "success",
  ERROR = "error",
}

/**
 * 请求方法枚举
 */
export enum RequestMethod {
  GET = "GET",
  POST = "POST",
  PUT = "PUT",
  DELETE = "DELETE",
  PATCH = "PATCH",
}

/**
 * 请求管理器接口
 */
export interface RequestManager {
  // 发起请求
  request<T = any>(config: RequestConfig): Promise<T>;
  // GET请求
  get<T = any>(url: string, config?: RequestConfig): Promise<T>;
  // POST请求
  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T>;
  // PUT请求
  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T>;
  // DELETE请求
  delete<T = any>(url: string, config?: RequestConfig): Promise<T>;
  // 取消请求
  cancelRequest(requestId: string): void;
  // 取消所有请求
  cancelAllRequests(): void;
}
