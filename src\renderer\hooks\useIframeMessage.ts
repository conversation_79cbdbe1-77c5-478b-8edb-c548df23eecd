import { useCallback, useEffect, useRef, useState } from "react";

type MessagePayload =
  | {
      type: "GET_VALUE" | "RESET_DEFAULT_VALUE";
    }
  | {
      type: "SET_VALUE";
      payload: Record<string, any>;
    }
  | {
      type: "GET_DOC_ARRAY_BUFFER";
      payload: "download" | "print" | "scanPdf" | "scanWord";
    };

type MessageType =
  | {
      type: "READY";
    }
  | {
      type: "POST_VALUE";
      payload: {
        data: Record<string, any>;
        emptyFields: string[];
      };
    }
  | {
      type: "SET_VALUE";
      payload: Record<string, any>;
    }
  | DocxMessage;

/**
 * 扫描word消息
 */
export type DocxMessage = {
  /**
   * 获取文档数组缓冲区
   */
  type: "POST_DOC_ARRAY_BUFFER";
  payload: {
    data: ArrayBuffer;
    type: "download" | "print" | "scanPdf" | "scanWord";
    emptyFields: string[];
  };
};

interface UseIframeMessengerOptions {
  /**
   * 目标源
   */
  targetOrigin: string;
  /**
   * 消息处理函数
   */
  onMessage?: (data: MessageType, event: MessageEvent) => void;
}

export type SendMessage = (message: MessagePayload) => void;

type UseIframeMessengerResult = {
  ready: boolean;
  iframeRef: React.RefObject<HTMLIFrameElement | null>;
  sendMessage: SendMessage;
};

const useIframeMessenger = ({
  targetOrigin,
  onMessage,
}: UseIframeMessengerOptions): UseIframeMessengerResult => {
  /**
   * iframe 引用
   */
  const iframeRef = useRef<HTMLIFrameElement | null>(null);

  /**
   * 是否就绪
   */
  const [ready, setReady] = useState(false);

  /**
   * 消息队列
   */
  const messageQueue = useRef<any[]>([]);

  /**
   * 发送消息队列
   */
  const flushMessageQueue = useCallback(() => {
    const sonWindow = iframeRef.current?.contentWindow;
    if (!ready || !sonWindow) return;

    for (const message of messageQueue.current) {
      sonWindow.postMessage(message, targetOrigin);
    }
    messageQueue.current = [];
  }, [targetOrigin, ready]);

  /**
   * 发送消息
   */
  const sendMessage = useCallback(
    (message: MessagePayload) => {
      const sonWindow = iframeRef.current?.contentWindow;

      if (!sonWindow || !ready) {
        // 👈 未就绪：先缓存
        messageQueue.current.push(message);
        console.warn("iframe 未准备好，已缓存消息：", message);
        return;
      }

      sonWindow.postMessage(message, targetOrigin);
    },
    [targetOrigin, ready]
  );

  useEffect(() => {
    const handleMessage = (event: MessageEvent<MessageType>) => {
      if (targetOrigin !== "*" && event.origin !== targetOrigin) return;

      if (event.data.type === "READY") {
        setReady(true);
        flushMessageQueue();
        return;
      }

      onMessage?.(event.data, event);
    };

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, [targetOrigin, onMessage, flushMessageQueue]);

  return {
    ready,
    iframeRef,
    sendMessage,
  };
};

export default useIframeMessenger;
