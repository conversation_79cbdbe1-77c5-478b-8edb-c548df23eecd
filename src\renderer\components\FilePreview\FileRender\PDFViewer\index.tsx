import * as pdfjsLib from "pdfjs-dist";
import React, { useState } from "react";
import { twMerge } from "tailwind-merge";
import type { FileRenderProps } from "../types";
import AllMode from "./components/AllMode";
import SingleMode from "./components/SignleMode";
import StatsBar from "./StatsBar";
import ToolBar from "./ToolBar";

const isDev = import.meta.env.MODE === "development";

pdfjsLib.GlobalWorkerOptions.workerSrc = isDev
  ? "/pdf/pdf.worker.min.mjs"
  : `./pdf/pdf.worker.min.mjs`;

interface PDFPreviewProps extends FileRenderProps {
  /** 初始缩放比例 */
  initialScale?: number;
  /** 是否显示工具栏 */
  showToolbar?: boolean;
  /** 是否显示缩略图 */
  showThumbnails?: boolean;
  /** 是否显示状态栏 */
  showStatsBar?: boolean;
  /** 显示模式 */
  mode: "single" | "all";
  /** 自定义样式 */
  className?: string;
  /** 骨架屏自定义样式 */
  skeletonClassName?: string;
}

const PDFPreview: React.FC<PDFPreviewProps> = ({
  filePath,
  fileName,
  initialScale = 1.0,
  showToolbar = false,
  showThumbnails = false,
  showStatsBar = false,
  mode = "all",
  className,
  skeletonClassName,
}) => {
  // 总页数
  const [totalPages, setTotalPages] = useState(0);
  // 当前页码
  const [currentPage, setCurrentPage] = useState(1);

  const classes = twMerge(
    "w-full h-max flex flex-col bg-white rounded-lg overflow-hidden relative",
    className
  );

  return (
    <div className={classes}>
      {/* 工具栏 */}
      {showToolbar && (
        <ToolBar
          displayMode={mode}
          onPageChange={setCurrentPage}
          currentPage={currentPage}
          totalPages={totalPages}
          filePath={filePath}
          fileName={fileName}
        />
      )}

      {/* 主要内容区域 */}
      {mode === "single" && (
        <SingleMode
          onPageChange={setCurrentPage}
          currentPage={currentPage}
          showThumbnails={showThumbnails}
          filePath={filePath}
          setTotalPages={setTotalPages}
        />
      )}

      {mode === "all" && (
        <AllMode
          filePath={filePath}
          initialScale={initialScale}
          skeletonClassName={skeletonClassName}
        />
      )}

      {/* 状态栏 */}
      {showStatsBar && (
        <StatsBar
          fileName={fileName}
          displayMode={mode}
          totalPages={totalPages}
          allPagesRendered={false}
          renderedPages={0}
        />
      )}
    </div>
  );
};

export default PDFPreview;
