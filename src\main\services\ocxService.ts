import type {
  OCXCommand,
  OCXMessageType,
  OCXMode,
  OCXVideoDispMode,
} from "@/renderer/devices/documentCamera/types";
import { IPC_CHANNELS } from "@/shared/constants";
import type { FileInfo } from "@/types";
import type { BrowserWindow } from "electron";
import fs from "fs";
import path from "path";
import { ipcManager } from "../ipc/IpcManager";
import { registerLocalFileProtocolHandler } from "../protocols/localFileProtocols";
import { getReadableUniqueFileName } from "../utils/file";

interface OcxServiceOptions {
  url?: string;
  win?: BrowserWindow | null;
}

class OcxService {
  private static instance: OcxService;

  /**
   * 窗口实例
   */
  private win: BrowserWindow;
  private ws: WebSocket | null = null;
  private url: string;
  private isReady = false;
  private queue: Array<{
    resolve: (data: any) => void;
    reject: (err: any) => void;
  }> = [];

  private constructor({
    url = "ws://localhost:1818",
    win,
  }: OcxServiceOptions = {}) {
    this.url = url;
    if (win) {
      this.win = win;
    }
    this.connect();
    this.setupIpcHandlers();
    registerLocalFileProtocolHandler();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(options?: OcxServiceOptions): OcxService {
    if (!OcxService.instance) {
      OcxService.instance = new OcxService(options);
    }
    return OcxService.instance;
  }

  /**
   * 设置IPC处理
   */
  private setupIpcHandlers() {
    ipcManager.registerHandler(IPC_CHANNELS.OCX.START_PLAY, () =>
      this.startPlay()
    );

    ipcManager.registerHandler(IPC_CHANNELS.OCX.STOP_PLAY, () =>
      this.stopPlay()
    );

    ipcManager.registerHandler(IPC_CHANNELS.OCX.SET_MODE, (_, mode) =>
      this.setMode(mode)
    );

    ipcManager.registerHandler(
      IPC_CHANNELS.OCX.SET_VIDEO_DISP_MODE,
      (_, mode) => this.setVideoDispMode(mode)
    );

    ipcManager.registerHandler(IPC_CHANNELS.OCX.TAKE_PHOTO, (_, type) =>
      this.takePhoto(type)
    );

    ipcManager.registerHandler(IPC_CHANNELS.OCX.CLEAR_FOLDER, () =>
      this.clearFolder()
    );

    ipcManager.registerHandler(IPC_CHANNELS.OCX.ANY, (_, data) => {
      this.sendRequest(data);
    });
  }

  /**
   * 连接 WebSocket
   */
  private async connect(): Promise<void> {
    if (this.ws && this.isReady) return Promise.resolve();
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.url);
      this.ws.onopen = () => {
        this.isReady = true;
        resolve();
      };
      this.ws.onerror = err => {
        this.isReady = false;
        reject(err);
      };
      this.ws.onclose = () => {
        this.isReady = false;
      };
      this.ws.onmessage = event => {
        const data = event.data as OCXMessageType;

        try {
          if (data.startsWith("Begin")) {
            // TODO: 解析请求响应
            this.handleResponse(data);
          } else {
            // 图片流
            this.handleImageStream(event.data);
          }
        } catch (e) {
          // todo: 解析失败
        }
      };
    });
  }

  /**
   * 断开 WebSocket
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.isReady = false;
    }
  }

  /**
   * 发送请求
   */
  private async sendRequest<T = any>(req: OCXCommand): Promise<T> {
    await this.connect();
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN)
      throw new Error("WebSocket not connected");
    return new Promise((resolve, reject) => {
      this.queue.push({ resolve, reject });

      // 拼接参数
      const params = Object.entries(req);
      this.ws?.send(
        `${req.type}(${params
          .slice(1)
          .map(item => item[1])
          .join(",")})`
      );
      // 超时处理
      setTimeout(() => {
        if (this.queue.length > 0) {
          const idx = this.queue.findIndex(item => item.resolve === resolve);
          if (idx !== -1) {
            this.queue.splice(idx, 1);
            reject("Timeout");
          }
        }
      }, 5000);
    });
  }

  /**
   * 处理请求响应
   * @param data 请求响应
   */
  private handleResponse(data: string) {
    if (this.queue.length > 0) {
      const { resolve } = this.queue.shift()!;
      resolve(data);
    }
  }

  /**
   * 处理图片流
   * @param data 图片流
   */
  private handleImageStream(data: string) {
    if (this.win) {
      this.win.webContents.send(IPC_CHANNELS.OCX.GET_IMAGE_STREAM, {
        base64: true,
        data,
      });
    }
  }

  /**
   * 开始拍摄
   */
  private async startPlay() {
    await this.sendRequest({ type: "bStartPlay" });
  }

  /**
   * 停止拍摄
   */
  private async stopPlay() {
    await this.sendRequest({ type: "bStopPlay" });
  }

  /**
   * 设置拍照模式
   */
  private async setMode(mode: OCXMode) {
    await this.sendRequest({ type: "bSetMode", mode });
  }

  /**
   * 设置视频显示模式
   */
  private async setVideoDispMode(mode: OCXVideoDispMode) {
    await this.sendRequest({ type: "vSetVideoDispMode", mode });
  }

  /**
   * 拍照
   */
  private async takePhoto(
    type: "JPG" | "PNG" | "TifJPG"
  ): Promise<FileInfo | null> {
    // 目标文件夹
    const target = "camera";
    const filePath = path.join(process.cwd(), "temp", target);

    // 创建文件夹
    await fs.promises.mkdir(filePath, { recursive: true });

    // 生成文件名
    const filename = getReadableUniqueFileName();

    // 生成文件扩展名
    const extension = type === "TifJPG" ? "jpg" : type.toLowerCase();

    // 生成文件路径
    const fullPath = path.join(filePath, filename) + "." + extension;

    try {
      // 发送拍照命令
      await this.sendRequest({
        type: `bSave${type}`,
        filePath: filePath + "\\",
        filename,
      });

      return {
        id: filename,
        name: filename,
        path: fullPath,
        type: "image",
        extension,
        size: 0,
        selected: true,
        remote: "ocx",
      };
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  /**
   * 清除文件夹
   */
  private async clearFolder() {
    await fs.promises.rm(path.join(process.cwd(), "temp", "camera"), {
      recursive: true,
      force: true,
    });
  }
}

export default OcxService;
