import * as pdfjsLib from "pdfjs-dist";
import { useCallback, useEffect, useRef, useState } from "react";
import usePDFCore from "./usePDFCore";

export const useSinglePagePDFViewer = (
  filePath: string,
  initialScale = 1.0
) => {
  const {
    pdfDocument,
    totalPages,
    scale,
    loading,
    error,
    setError,
    currentPage,
    goToPage,
    zoomIn,
    zoomOut,
    resetZoom,
    cancelAllTasks,
  } = usePDFCore(filePath, initialScale);

  // 缩略图
  const [thumbnails, setThumbnails] = useState<{ [page: number]: string }>({});

  // 页面信息
  const [pageInfo, setPageInfo] = useState<{
    pageNumber: number;
    width: number;
    height: number;
    scale: number;
  } | null>(null);

  // 渲染任务
  const renderTaskRef = useRef<pdfjsLib.RenderTask | null>(null);
  // 取消标志
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * 取消渲染任务
   */
  const cancelRenderTasks = useCallback(() => {
    // 取消渲染任务
    if (renderTaskRef.current) {
      renderTaskRef.current.cancel();
      renderTaskRef.current = null;
    }

    // 取消AbortController
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  /**
   * 渲染页面
   * @param pageNumber - 页码
   * @param canvas - 画布
   */
  const renderPage = useCallback(
    async (pageNumber: number, canvas: HTMLCanvasElement) => {
      if (!pdfDocument) return;

      try {
        const page = await pdfDocument.getPage(pageNumber);
        const context = canvas.getContext("2d");
        if (!context) throw new Error("canvas context 获取失败");

        const viewport = page.getViewport({ scale });
        canvas.width = viewport.width;
        canvas.height = viewport.height;

        setPageInfo({
          pageNumber,
          width: viewport.width,
          height: viewport.height,
          scale,
        });

        // 取消之前的渲染任务
        if (renderTaskRef.current) renderTaskRef.current.cancel();

        const renderTask = page.render({ canvasContext: context, viewport });
        renderTaskRef.current = renderTask;
        await renderTask.promise;
        renderTaskRef.current = null;
      } catch (err) {
        if (err?.name !== "RenderingCancelledException") {
          setError(err instanceof Error ? err.message : "渲染失败");
        }
      }
    },
    [pdfDocument, scale, setError]
  );

  /**
   * 生成缩略图
   * @param pageNumber - 页码
   */
  const generateThumbnail = useCallback(
    async (pageNumber: number) => {
      if (!pdfDocument || thumbnails[pageNumber]) return;

      try {
        const page = await pdfDocument.getPage(pageNumber);
        const viewport = page.getViewport({ scale: 0.2 }); // 缩略图使用较小缩放

        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");

        if (!context) return;

        canvas.width = viewport.width;
        canvas.height = viewport.height;

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        await page.render(renderContext).promise;

        const thumbnailUrl = canvas.toDataURL();
        setThumbnails(prev => ({ ...prev, [pageNumber]: thumbnailUrl }));
      } catch (err) {
        console.error(`缩略图生成失败 (页面 ${pageNumber}):`, err);
      }
    },
    [pdfDocument, thumbnails]
  );

  /**
   * 生成缩略图
   * @param pageNumber - 页码
   */
  useEffect(() => {
    if (!pdfDocument) return;

    const pagesToGen = [currentPage];
    if (currentPage > 1) pagesToGen.push(currentPage - 1);
    if (currentPage < totalPages) pagesToGen.push(currentPage + 1);

    pagesToGen.forEach(page => {
      if (!thumbnails[page]) generateThumbnail(page);
    });
  }, [currentPage, pdfDocument, totalPages, thumbnails, generateThumbnail]);

  // 清理函数
  useEffect(() => {
    return () => {
      cancelRenderTasks();
    };
  }, [cancelRenderTasks]);

  return {
    thumbnails,
    pdfDocument,
    loading,
    currentPage,
    totalPages,
    scale,
    error,
    pageInfo,
    renderPage,
    goToPage,
    zoomIn,
    zoomOut,
    resetZoom,
    cancelAllTasks,
    cancelRenderTasks,
  };
};
